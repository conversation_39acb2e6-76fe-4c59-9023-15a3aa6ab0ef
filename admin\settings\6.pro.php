<?php
/*
 * Page Name: PRO FEATURES 🚀
 */

$features = [

	[
		'icon'  => 'fa-solid fa-terminal',
		'bg'    => '#e7332e',
		'title' => __( 'Tooltip', 'button-generation' ),
		'desc'  => __( ' Show helpful notes when hovering over buttons.', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-swatchbook',
		'bg'    => '#4CAF50',
		'title' => __( 'Stylish Hover Animations', 'button-generation' ),
		'desc'  => __( 'Bring your buttons to life with smooth hover transitions. ', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-info-circle',
		'bg'    => '#4ec477',
		'title' => __( 'Informative Badges', 'button-generation' ),
		'desc'  => __( 'Add informative badges to your buttons. These badges act like mini signs that provide additional context or value to users, boosting the effectiveness of your buttons.', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-file',
		'bg'    => '#e7332e',
		'title' => __( 'Flexible Placement', 'button-generation' ),
		'desc'  => __( 'Auto-insert buttons before/after post content. ', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-square',
		'bg'    => '#e7332e',
		'title' => __( 'Background Hover Effect', 'button-generation' ),
		'desc'  => __( 'Shift background color on hover.', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-icons',
		'bg'    => '#e7332e',
		'title' => __( 'Icon Hover Effect', 'button-generation' ),
		'desc'  => __( ' Animate icon behavior during hover.', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-border-top-left',
		'bg'    => '#e7332e',
		'title' => __( 'Border Hover Effect', 'button-generation' ),
		'desc'  => __( 'Add slick animated borders.', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-check-double',
		'bg'    => '#e7332e',
		'title' => __( 'Attention Seekers', 'button-generation' ),
		'desc'  => __( '13 built-in animations to grab attention. ', 'button-generation' ),
	],


	[
		'icon'  => 'fa-solid fa-image',
		'bg'    => '#2196F3',
		'title' => __( 'Custom Icons, Emojis & Letters', 'button-generation' ),
		'desc'  => __( 'Upload your own icons or use emojis and letters for creative, unique, or casual designs. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-share-nodes',
		'bg'    => '#e7332e',
		'title' => __( 'Social Sharing', 'button-generation' ),
		'desc'  => __( 'Include social sharing links from 30+ services to help users share your content and boost traffic. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-language',
		'bg'    => '#c65ebc',
		'title' => __( 'Translate', 'button-generation' ),
		'desc'  => __( 'Let visitors translate your website content in real time. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-print',
		'bg'    => '#5fddc5',
		'title' => __( 'Print', 'button-generation' ),
		'desc'  => __( 'PAdd a quick-access print button that launches the browser print dialog for easy page printing.', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-arrow-right-arrow-left',
		'bg'    => '#437fb2',
		'title' => __( 'Next / Previous Post Navigation', 'button-generation' ),
		'desc'  => __( 'Automatically link to the next or previous post within the same category to keep visitors engaged and moving through your content.', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-arrows-up-down',
		'bg'    => '#f2c35d',
		'title' => __( 'Scroll to Top / Bottom', 'button-generation' ),
		'desc'  => __( 'Add one-click scroll links that take users instantly to the top or bottom of the page — ideal for long content pages. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-link',
		'bg'    => '#ea84ad',
		'title' => __( 'Activate by URL', 'button-generation' ),
		'desc'  => __( 'Display the button only when the current page URL contains specific keywords or parameters. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-handshake-angle',
		'bg'    => '#437fb2',
		'title' => __( 'Activate by Referrer URL', 'button-generation' ),
		'desc'  => __( 'Trigger button display based on the visitor’s referrer — useful for targeting traffic from ads or campaigns.  ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-display',
		'bg'    => '#ff9800',
		'title' => __( 'Comprehensive Display Controls', 'button-generation' ),
		'desc'  => __( 'Use shortcodes, post types, tags, categories, or archive pages to fine-tune where and when your menu appears. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-tablet-screen-button',
		'bg'    => '#df6928',
		'title' => __( 'Device-Based Visibility', 'button-generation' ),
		'desc'  => __( 'Remove the button entirely on mobile or desktop devices to optimize performance and layout per screen. ', 'button-generation' ),
	],

    [
		'icon'  => 'fa-solid fa-users',
		'bg'    => '#4CAF50',
		'title' => __( 'User Role Permissions', 'button-generation' ),
		'desc'  => __( 'Restrict button visibility for specific user roles (e.g., Admins, Editors, Subscribers).', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-language',
		'bg'    => '#673ab7',
		'title' => __( 'Multilingual Support', 'button-generation' ),
		'desc'  => __( 'Show button based on language preferences. Compatible with WPML and Polylang.', 'button-generation' ),
	],
	[
		'icon'  => 'fa-solid fa-calendar-day',
		'bg'    => '#3f51b5',
		'title' => __( 'Scheduling', 'button-generation' ),
		'desc'  => __( 'Schedule button appearances based on specific days, times, and dates. This allows you to promote temporary events or campaigns without cluttering your website permanently. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-brands fa-chrome',
		'bg'    => '#3f51b5',
		'title' => __( 'Browser Compatibility', 'button-generation' ),
		'desc'  => __( 'Ensure your buttons display correctly across a wide range of browsers. If necessary, you can choose to hide menus for specific browsers to address compatibility issues with outdated software versions. ', 'button-generation' ),
	],

	[
		'icon'  => 'fa-solid fa-chart-line',
		'bg'    => '#3f51b5',
		'title' => __( 'Google Analytics Integration', 'button-generation' ),
		'desc'  => __( ' Track click behavior directly in GA. ', 'button-generation' ),
	],
];

?>

<div class="wowp-pro-upgrade">
    <div>
        <h3>Unlock PRO Features</h3>
        <p>Upgrade to Button Generator Pro  and get advanced features like</p>
        <a href="https://wow-estore.com/item/button-generator-pro/" target="_blank" class="button button-primary">Get Button Generator Pro </a>
    </div>
    <dl class="wowp-pro__profits">
        <div class="wowp-pro__profit">
            <dt><span class="wpie-icon wpie_icon-money-time"></span>No Yearly Fees</dt>
            <dd>One-time payment. Use it forever.</dd>
        </div>
        <div class="wowp-pro__profit">
            <dt><span class="wpie-icon wpie_icon-refund"></span>14-Day Money-Back Guarantee</dt>
            <dd>Try it risk-free. Get a full refund if you’re not satisfied.</dd>
        </div>
        <div class="wowp-pro__profit">
            <dt><span class="wpie-icon wpie_icon-cloud-data-sync"></span>Lifetime Free Updates</dt>
            <dd>Always stay up to date — at no extra cost.</dd>
        </div>
        <div class="wowp-pro__profit">
            <dt><span class="wpie-icon wpie_icon-customer-support"></span>Priority Support</dt>
            <dd>Fast, friendly, and expert help whenever you need it.</dd>
        </div>
    </dl>

</div>

<div class="wowp-pro-features">

	<?php foreach ( $features as $feature ) : ?>

		<?php if ( ! empty( $feature['icon'] ) ): ?>
            <div class="wowp-pro-feature">
                <div class="wowp-pro-feature__icon" style="background: <?php echo esc_attr( $feature['bg'] ); ?>">
                    <span class="<?php echo esc_attr( $feature['icon'] ); ?>"></span>
                </div>
                <div class="wowp-pro-feature__content">
                    <div class="wowp-pro-feature__title">
						<?php echo esc_html( $feature['title'] ); ?>
                    </div>
                    <div class="wowp-pro-feature__desc">
						<?php echo esc_html( $feature['desc'] ); ?>
                    </div>
                </div>
            </div>
		<?php endif; ?>
	<?php endforeach; ?>
</div>
<?php
