/*!
 * ========= INFORMATION ============================
 * - document:  Button Generator Pro
 * - brand:     Wow-Company
 * - brand-url: https://wow-company.com/
 * - store-url: https://wow-estore.com/
 * - author:    <PERSON><PERSON><PERSON>
 * - url:       https://wow-estore.com/item/button-generator-pro/
 * ====================================================== */
.btg-button {
  text-decoration: none;
  border: none;
  margin: 0;
  padding: 0;
  width: auto;
  overflow: visible;
  background: transparent;
  color: inherit;
  font: inherit;
  line-height: normal;
  -webkit-font-smoothing: inherit;
  -moz-osx-font-smoothing: inherit;
  -webkit-appearance: none;
  visibility: visible !important;
  box-sizing: border-box;
}

.btg-button__group {
  --group-gap: 8px;
  --group-direction: row;
  --group-align-items: center;
  display: -webkit-flex;
  display: flex;
  gap: var(--group-gap);
  -webkit-flex-direction: var(--group-direction);
  flex-direction: var(--group-direction);
  -webkit-align-items: var(--group-align-items);
  align-items: var(--group-align-items);
  margin-block: 1rem;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
}

.btg-button {
  --position: relative;
  --direction: row;
  --gap: 8px;
  --width: 100px;
  --height: 50px;
  --z-index: 999;
  --color: #ffffff;
  --background: #1f9ef8;
  --hover-color: #ffffff;
  --hover-background: #0090f7;
  --icon-hover-color: #ffffff;
  --radius: 1px;
  --border-style: none;
  --border-color: #383838;
  --border-width: 1px;
  --shadow: none;
  --font-size: 16px;
  --font-family: inherit;
  --font-weight: normal;
  --font-style: normal;
  --rotate: 0deg;
  --transition-duration: 0.2s;
  --transition-function: ease;
  position: var(--position);
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: var(--gap);
  -webkit-flex-direction: var(--direction);
  flex-direction: var(--direction);
  width: var(--width);
  height: var(--height);
  z-index: var(--z-index);
  color: var(--color);
  background: var(--background);
  box-shadow: var(--shadow);
  border-radius: var(--radius);
  border-style: var(--border-style);
  border-width: var(--border-width);
  border-color: var(--border-color);
  font-size: var(--font-size);
  font-family: var(--font-family);
  font-weight: var(--font-weight);
  font-style: var(--font-style);
  cursor: pointer;
  rotate: var(--rotate);
  transition-duration: var(--transition-duration);
  transition-property: all;
  transition-timing-function: var(--transition-function);
}
.btg-button:active, .btg-button:focus, .btg-button:hover {
  color: var(--hover-color);
  background: var(--hover-background);
  border-color: var(--border-color);
}
.btg-button:active .btg-icon, .btg-button:focus .btg-icon, .btg-button:hover .btg-icon {
  color: var(--icon-hover-color);
}
.btg-button .btg-icon {
  --rotate: 0deg;
  --font-size: 16px;
  --color: #ffffff;
  rotate: var(--rotate);
  font-size: var(--font-size);
  color: var(--color);
}
.btg-button img.btg-icon {
  --rotate: 0deg;
  --font-size: 16px;
  rotate: var(--rotate);
  max-width: var(--font-size);
  max-height: var(--font-size);
}
.btg-button .badge {
  --width: 25px;
  --height: 25px;
  --color: #ffffff;
  --background: #e95645;
  --radius: 25px;
  --border-style: none;
  --border-color: #ffffff;
  --border-width: 1px;
  --font-size: 12px;
  --font-family: inherit;
  --font-weight: normal;
  --font-style: normal;
  --top: -10px;
  --right: -10px;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: var(--width);
  height: var(--height);
  position: absolute;
  border-radius: var(--radius);
  border-style: var(--border-style);
  border-color: var(--border-color);
  border-width: var(--border-width);
  color: var(--color) !important;
  background-color: var(--background) !important;
  font-family: var(--font-family);
  font-size: var(--font-size);
  font-weight: var(--font-weight);
  font-style: var(--font-style);
  text-align: center;
  top: var(--top);
  right: var(--right);
}
.btg-button.btn-animation {
  --duration: 1s;
  --delay: 1s;
  --count: infinite;
  -webkit-animation-duration: var(--duration);
  animation-duration: var(--duration);
  -webkit-animation-delay: var(--delay);
  animation-delay: var(--delay);
  -webkit-animation-iteration-count: var(--count);
  animation-iteration-count: var(--count);
}
.btg-button .fa-rotate-90 {
  rotate: 90deg;
}
.btg-button .fa-rotate-180 {
  rotate: 180deg;
}
.btg-button .fa-rotate-270 {
  rotate: 270deg;
}

.btg-center {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.btg-topLeft {
  top: 0;
  left: 0;
}

.btg-topCenter {
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
}

.btg-topRight {
  top: 0;
  right: 0;
}

.btg-bottomLeft {
  bottom: 0;
  left: 0;
}

.btg-bottomCenter {
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
}

.btg-bottomRight {
  bottom: 0;
  right: 0;
}

.btg-right {
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto 0;
}

.btg-left {
  top: 0;
  left: 0;
  bottom: 0;
  margin: auto 0;
}

.btg-rotate-90 {
  rotate: 90deg;
}

.btg-rotate-180 {
  rotate: 180deg;
}

.btg-rotate-270 {
  rotate: 270deg;
}

.btg-button .btn-tooltiptext {
  visibility: hidden;
  box-sizing: border-box;
  min-width: 50px;
  width: inherit;
  line-height: 1.2;
  background-color: #555;
  color: #fff;
  border-radius: 4px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.875rem;
}

.btg-button .btn-tooltiptext::after {
  content: "";
  position: absolute;
  border-width: 5px;
  border-style: solid;
}

.btg-button:hover .btn-tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Top position */
.btn-tooltiptext.tooltip-top {
  bottom: 125%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.btn-tooltiptext.tooltip-top::after {
  top: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  border-color: #555 transparent transparent transparent;
}

/* Bottom position */
.btn-tooltiptext.tooltip-bottom {
  top: 125%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.btn-tooltiptext.tooltip-bottom::after {
  bottom: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  border-color: transparent transparent #555 transparent;
}

/* Left position */
.btn-tooltiptext.tooltip-left {
  top: 50%;
  right: 110%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.btn-tooltiptext.tooltip-left::after {
  top: 50%;
  left: 100%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-color: transparent transparent transparent #555;
}

/* Right position */
.btn-tooltiptext.tooltip-right {
  top: 50%;
  left: 110%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.btn-tooltiptext.tooltip-right::after {
  top: 50%;
  right: 100%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-color: transparent #555 transparent transparent;
}

body.has-google-translate {
  top: 0 !important;
}
body.has-google-translate .skiptranslate {
  display: none;
}

.btn-animation {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-play-state: running;
  animation-play-state: running;
}
.btn-animation:hover {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}
.btn-animation.btn-bounce {
  -webkit-animation-name: btn-bounce;
  animation-name: btn-bounce;
  -webkit-transform-origin: center center;
  transform-origin: center center;
}
.btn-animation.btn-flash {
  -webkit-animation-name: btn-flash;
  animation-name: btn-flash;
}
.btn-animation.btn-headShake {
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-name: btn-headShake;
  animation-name: btn-headShake;
}
.btn-animation.btn-jello {
  -webkit-animation-name: btn-jello;
  animation-name: btn-jello;
  -webkit-transform-origin: center;
  transform-origin: center;
}
.btn-animation.btn-heartBeat {
  -webkit-animation-name: btn-heartBeat;
  animation-name: btn-heartBeat;
  -webkit-animation-duration: calc(1s * 1.3);
  animation-duration: calc(1s * 1.3);
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
}
.btn-animation.btn-pulse {
  -webkit-animation-name: btn-pulse;
  animation-name: btn-pulse;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
}
.btn-animation.btn-rubberBand {
  -webkit-animation-name: btn-rubberBand;
  animation-name: btn-rubberBand;
}
.btn-animation.btn-shake {
  -webkit-animation-name: btn-shake;
  animation-name: btn-shake;
}
.btn-animation.btn-shakeX {
  -webkit-animation-name: btn-shakeX;
  animation-name: btn-shakeX;
}
.btn-animation.btn-shakeY {
  -webkit-animation-name: btn-shakeY;
  animation-name: btn-shakeY;
}
.btn-animation.btn-swing {
  -webkit-transform-origin: center center;
  transform-origin: center center;
  -webkit-animation-name: btn-swing;
  animation-name: btn-swing;
}
.btn-animation.btn-tada {
  -webkit-animation-name: btn-tada;
  animation-name: btn-tada;
}
.btn-animation.btn-wobble {
  -webkit-animation-name: btn-wobble;
  animation-name: btn-wobble;
}

.btg-button._grow:hover, .btg-button._grow:focus, .btg-button._grow:active {
  scale: 1.1;
}
.btg-button._shrink:hover, .btg-button._shrink:focus, .btg-button._shrink:active {
  scale: 0.9;
}
.btg-button._pulse:hover, .btg-button._pulse:focus, .btg-button._pulse:active, .btg-button._pulse-grow:hover, .btg-button._pulse-grow:focus, .btg-button._pulse-grow:active, .btg-button._pulse-shrink:hover, .btg-button._pulse-shrink:focus, .btg-button._pulse-shrink:active, .btg-button._push:hover, .btg-button._push:focus, .btg-button._push:active, .btg-button._pop:hover, .btg-button._pop:focus, .btg-button._pop:active, .btg-button._buzz:hover, .btg-button._buzz:focus, .btg-button._buzz:active, .btg-button._buzz-out:hover, .btg-button._buzz-out:focus, .btg-button._buzz-out:active {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-direction: alternate;
  animation-direction: alternate;
}
.btg-button._pulse:hover, .btg-button._pulse:focus, .btg-button._pulse:active {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-direction: normal;
  animation-direction: normal;
  -webkit-animation-name: _pulse;
  animation-name: _pulse;
}
.btg-button._pulse-grow:hover, .btg-button._pulse-grow:focus, .btg-button._pulse-grow:active {
  -webkit-animation-name: _pulse-grow;
  animation-name: _pulse-grow;
}
.btg-button._pulse-shrink:hover, .btg-button._pulse-shrink:focus, .btg-button._pulse-shrink:active {
  -webkit-animation-name: _pulse-shrink;
  animation-name: _pulse-shrink;
}
.btg-button._push:hover, .btg-button._push:focus, .btg-button._push:active {
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-name: _push;
  animation-name: _push;
}
.btg-button._pop:hover, .btg-button._pop:focus, .btg-button._pop:active {
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-name: _pop;
  animation-name: _pop;
}
.btg-button._buzz:hover, .btg-button._buzz:focus, .btg-button._buzz:active {
  -webkit-animation-name: _buzz;
  animation-name: _buzz;
  -webkit-animation-duration: 0.15s;
  animation-duration: 0.15s;
}
.btg-button._buzz-out:hover, .btg-button._buzz-out:focus, .btg-button._buzz-out:active {
  -webkit-animation-name: _buzz-out;
  animation-name: _buzz-out;
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}
.btg-button._bounce-in, .btg-button._bounce-out {
  transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
}
.btg-button._bounce-in:hover, .btg-button._bounce-in:focus, .btg-button._bounce-in:active {
  scale: 1.2;
}
.btg-button._bounce-out:hover, .btg-button._bounce-out:focus, .btg-button._bounce-out:active {
  scale: 0.8;
}
.btg-button._rotate:hover, .btg-button._rotate:focus, .btg-button._rotate:active {
  rotate: 4deg;
}
.btg-button._grow-rotate:hover, .btg-button._grow-rotate:focus, .btg-button._grow-rotate:active {
  scale: 1.1;
  rotate: 4deg;
}
.btg-button._float:hover, .btg-button._float:focus, .btg-button._float:active {
  translate: 0 -8px;
}
.btg-button._sink:hover, .btg-button._sink:focus, .btg-button._sink:active {
  translate: 0 8px;
}
.btg-button._bob:hover, .btg-button._bob:focus, .btg-button._bob:active, .btg-button._hang:hover, .btg-button._hang:focus, .btg-button._hang:active {
  -webkit-animation-duration: 0.3s, 1.5s;
  animation-duration: 0.3s, 1.5s;
  -webkit-animation-delay: 0s, 0.3s;
  animation-delay: 0s, 0.3s;
  -webkit-animation-timing-function: ease-out, ease-in-out;
  animation-timing-function: ease-out, ease-in-out;
  -webkit-animation-iteration-count: 1, infinite;
  animation-iteration-count: 1, infinite;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-direction: normal, alternate;
  animation-direction: normal, alternate;
}
.btg-button._bob:hover, .btg-button._bob:focus, .btg-button._bob:active {
  -webkit-animation-name: _bob-float, _bob;
  animation-name: _bob-float, _bob;
}
.btg-button._hang:hover, .btg-button._hang:focus, .btg-button._hang:active {
  -webkit-animation-name: _hang-sink, _hang;
  animation-name: _hang-sink, _hang;
}
.btg-button._skew:hover, .btg-button._skew:focus, .btg-button._skew:active {
  -webkit-transform: skew(-10deg);
  transform: skew(-10deg);
}
.btg-button._skew-forward {
  rotate: 0deg !important;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
}
.btg-button._skew-forward:hover, .btg-button._skew-forward:focus, .btg-button._skew-forward:active {
  -webkit-transform: skew(-10deg);
  transform: skew(-10deg);
}
.btg-button._skew-backward {
  rotate: 0deg !important;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
}
.btg-button._skew-backward:hover, .btg-button._skew-backward:focus, .btg-button._skew-backward:active {
  -webkit-transform: skew(10deg);
  transform: skew(10deg);
}
.btg-button._wobble-horizontal:hover, .btg-button._wobble-horizontal:focus, .btg-button._wobble-horizontal:active, .btg-button._wobble-vertical:hover, .btg-button._wobble-vertical:focus, .btg-button._wobble-vertical:active, .btg-button._wobble-to-bottom-right:hover, .btg-button._wobble-to-bottom-right:focus, .btg-button._wobble-to-bottom-right:active, .btg-button._wobble-to-top-right:hover, .btg-button._wobble-to-top-right:focus, .btg-button._wobble-to-top-right:active, .btg-button._wobble-top:hover, .btg-button._wobble-top:focus, .btg-button._wobble-top:active, .btg-button._wobble-bottom:hover, .btg-button._wobble-bottom:focus, .btg-button._wobble-bottom:active, .btg-button._wobble-skew:hover, .btg-button._wobble-skew:focus, .btg-button._wobble-skew:active {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}
.btg-button._wobble-horizontal:hover, .btg-button._wobble-horizontal:focus, .btg-button._wobble-horizontal:active {
  -webkit-animation-name: _wobble-horizontal;
  animation-name: _wobble-horizontal;
}
.btg-button._wobble-vertical:hover, .btg-button._wobble-vertical:focus, .btg-button._wobble-vertical:active {
  -webkit-animation-name: _wobble-vertical;
  animation-name: _wobble-vertical;
}
.btg-button._wobble-to-bottom-right:hover, .btg-button._wobble-to-bottom-right:focus, .btg-button._wobble-to-bottom-right:active {
  -webkit-animation-name: _wobble-to-bottom-right;
  animation-name: _wobble-to-bottom-right;
}
.btg-button._wobble-to-top-right:hover, .btg-button._wobble-to-top-right:focus, .btg-button._wobble-to-top-right:active {
  -webkit-animation-name: _wobble-to-top-right;
  animation-name: _wobble-to-top-right;
}
.btg-button._wobble-top {
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  rotate: 0deg !important;
}
.btg-button._wobble-top:hover, .btg-button._wobble-top:focus, .btg-button._wobble-top:active {
  -webkit-animation-name: _wobble-top;
  animation-name: _wobble-top;
}
.btg-button._wobble-bottom {
  -webkit-transform-origin: 100% 0;
  transform-origin: 100% 0;
  rotate: 0deg !important;
}
.btg-button._wobble-bottom:hover, .btg-button._wobble-bottom:focus, .btg-button._wobble-bottom:active {
  -webkit-animation-name: _wobble-bottom;
  animation-name: _wobble-bottom;
}
.btg-button._wobble-skew:hover, .btg-button._wobble-skew:focus, .btg-button._wobble-skew:active {
  -webkit-animation-name: _wobble-skew;
  animation-name: _wobble-skew;
}
.btg-button._forward:hover, .btg-button._forward:focus, .btg-button._forward:active {
  translate: 8px;
}
.btg-button._backward:hover, .btg-button._backward:focus, .btg-button._backward:active {
  translate: -8px;
}

.btg-button._back-pulse:hover, .btg-button._back-pulse:focus, .btg-button._back-pulse:active {
  -webkit-animation-name: _back-pulse;
  animation-name: _back-pulse;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.btg-button._sweep-to-right:before, .btg-button._sweep-to-left:before, .btg-button._sweep-to-bottom:before, .btg-button._sweep-to-top:before, .btg-button._bounce-to-right:before, .btg-button._bounce-to-left:before, .btg-button._bounce-to-bottom:before, .btg-button._bounce-to-top:before, .btg-button._radial-out:before, .btg-button._radial-in:before, .btg-button._rectangle-in:before, .btg-button._rectangle-out:before, .btg-button._shutter-in-horizontal:before, .btg-button._shutter-out-horizontal:before, .btg-button._shutter-in-vertical:before, .btg-button._shutter-out-vertical:before {
  content: "";
  position: absolute;
  z-index: -1;
  transition-duration: 0.3s;
  transition-timing-function: ease-out;
  background: var(--hover-background);
}
.btg-button._sweep-to-right:hover, .btg-button._sweep-to-right:focus, .btg-button._sweep-to-right:active, .btg-button._sweep-to-left:hover, .btg-button._sweep-to-left:focus, .btg-button._sweep-to-left:active, .btg-button._sweep-to-bottom:hover, .btg-button._sweep-to-bottom:focus, .btg-button._sweep-to-bottom:active, .btg-button._sweep-to-top:hover, .btg-button._sweep-to-top:focus, .btg-button._sweep-to-top:active, .btg-button._bounce-to-right:hover, .btg-button._bounce-to-right:focus, .btg-button._bounce-to-right:active, .btg-button._bounce-to-left:hover, .btg-button._bounce-to-left:focus, .btg-button._bounce-to-left:active, .btg-button._bounce-to-bottom:hover, .btg-button._bounce-to-bottom:focus, .btg-button._bounce-to-bottom:active, .btg-button._bounce-to-top:hover, .btg-button._bounce-to-top:focus, .btg-button._bounce-to-top:active, .btg-button._radial-out:hover, .btg-button._radial-out:focus, .btg-button._radial-out:active, .btg-button._radial-in:hover, .btg-button._radial-in:focus, .btg-button._radial-in:active, .btg-button._rectangle-in:hover, .btg-button._rectangle-in:focus, .btg-button._rectangle-in:active, .btg-button._rectangle-out:hover, .btg-button._rectangle-out:focus, .btg-button._rectangle-out:active, .btg-button._shutter-in-horizontal:hover, .btg-button._shutter-in-horizontal:focus, .btg-button._shutter-in-horizontal:active, .btg-button._shutter-out-horizontal:hover, .btg-button._shutter-out-horizontal:focus, .btg-button._shutter-out-horizontal:active, .btg-button._shutter-in-vertical:hover, .btg-button._shutter-in-vertical:focus, .btg-button._shutter-in-vertical:active, .btg-button._shutter-out-vertical:hover, .btg-button._shutter-out-vertical:focus, .btg-button._shutter-out-vertical:active {
  background: var(--background);
}
.btg-button._bounce-to-right:before, .btg-button._sweep-to-right:before {
  top: 0;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 0;
}
.btg-button._bounce-to-right:hover:before, .btg-button._bounce-to-right:focus:before, .btg-button._bounce-to-right:active:before, .btg-button._sweep-to-right:hover:before, .btg-button._sweep-to-right:focus:before, .btg-button._sweep-to-right:active:before {
  width: 100%;
}
.btg-button._bounce-to-left:before, .btg-button._sweep-to-left:before {
  top: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 0;
}
.btg-button._bounce-to-left:hover:before, .btg-button._bounce-to-left:focus:before, .btg-button._bounce-to-left:active:before, .btg-button._sweep-to-left:hover:before, .btg-button._sweep-to-left:focus:before, .btg-button._sweep-to-left:active:before {
  width: 100%;
}
.btg-button._bounce-to-bottom:before, .btg-button._sweep-to-bottom:before {
  top: 0;
  right: 0;
  left: 0;
  height: 0;
  width: 100%;
}
.btg-button._bounce-to-bottom:hover:before, .btg-button._bounce-to-bottom:focus:before, .btg-button._bounce-to-bottom:active:before, .btg-button._sweep-to-bottom:hover:before, .btg-button._sweep-to-bottom:focus:before, .btg-button._sweep-to-bottom:active:before {
  height: 100%;
}
.btg-button._bounce-to-top:before, .btg-button._sweep-to-top:before {
  bottom: 0;
  right: 0;
  left: 0;
  height: 0;
  width: 100%;
}
.btg-button._bounce-to-top:hover:before, .btg-button._bounce-to-top:focus:before, .btg-button._bounce-to-top:active:before, .btg-button._sweep-to-top:hover:before, .btg-button._sweep-to-top:focus:before, .btg-button._sweep-to-top:active:before {
  height: 100%;
}
.btg-button._bounce-to-right:hover:before, .btg-button._bounce-to-right:focus:before, .btg-button._bounce-to-right:active:before, .btg-button._bounce-to-left:hover:before, .btg-button._bounce-to-left:focus:before, .btg-button._bounce-to-left:active:before, .btg-button._bounce-to-bottom:hover:before, .btg-button._bounce-to-bottom:focus:before, .btg-button._bounce-to-bottom:active:before, .btg-button._bounce-to-top:hover:before, .btg-button._bounce-to-top:focus:before, .btg-button._bounce-to-top:active:before {
  transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
}
.btg-button._radial-out:before, .btg-button._radial-in:before, .btg-button._rectangle-in:before, .btg-button._rectangle-out:before, .btg-button._shutter-in-horizontal:before, .btg-button._shutter-out-horizontal:before, .btg-button._shutter-in-vertical:before, .btg-button._shutter-out-vertical:before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.btg-button._shutter-in-vertical:before, .btg-button._shutter-in-horizontal:before, .btg-button._rectangle-in:before, .btg-button._radial-in:before {
  background: var(--background);
}
.btg-button._shutter-in-vertical:hover, .btg-button._shutter-in-vertical:focus, .btg-button._shutter-in-vertical:active, .btg-button._shutter-in-horizontal:hover, .btg-button._shutter-in-horizontal:focus, .btg-button._shutter-in-horizontal:active, .btg-button._rectangle-in:hover, .btg-button._rectangle-in:focus, .btg-button._rectangle-in:active, .btg-button._radial-in:hover, .btg-button._radial-in:focus, .btg-button._radial-in:active {
  background: var(--hover-background);
}
.btg-button._radial-out, .btg-button._radial-in {
  overflow: hidden;
}
.btg-button._radial-out:before, .btg-button._radial-in:before {
  border-radius: 100%;
}
.btg-button._radial-out:before {
  -webkit-transform: scale(0);
  transform: scale(0);
}
.btg-button._radial-out:hover:before, .btg-button._radial-out:focus:before, .btg-button._radial-out:active:before {
  -webkit-transform: scale(2);
  transform: scale(2);
}
.btg-button._radial-in:before {
  -webkit-transform: scale(2);
  transform: scale(2);
}
.btg-button._radial-in:hover:before, .btg-button._radial-in:focus:before, .btg-button._radial-in:active:before {
  -webkit-transform: scale(0);
  transform: scale(0);
}
.btg-button._rectangle-in:before {
  scale: 1;
}
.btg-button._rectangle-in:hover:before, .btg-button._rectangle-in:focus:before, .btg-button._rectangle-in:active:before {
  scale: 0;
}
.btg-button._rectangle-out:before {
  scale: 0;
}
.btg-button._rectangle-out:hover:before, .btg-button._rectangle-out:focus:before, .btg-button._rectangle-out:active:before {
  scale: 1;
}
.btg-button._shutter-in-horizontal:before {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}
.btg-button._shutter-in-horizontal:hover:before, .btg-button._shutter-in-horizontal:focus:before, .btg-button._shutter-in-horizontal:active:before {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
}
.btg-button._shutter-out-horizontal:before {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
}
.btg-button._shutter-out-horizontal:hover:before, .btg-button._shutter-out-horizontal:focus:before, .btg-button._shutter-out-horizontal:active:before {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}
.btg-button._shutter-in-vertical:before {
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
}
.btg-button._shutter-in-vertical:hover:before, .btg-button._shutter-in-vertical:focus:before, .btg-button._shutter-in-vertical:active:before {
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
}
.btg-button._shutter-out-vertical:before {
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
}
.btg-button._shutter-out-vertical:hover:before, .btg-button._shutter-out-vertical:focus:before, .btg-button._shutter-out-vertical:active:before {
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
}

.btg-button._border-trim, .btg-button._border-ripple-out, .btg-button._border-ripple-in, .btg-button._border-outline-out, .btg-button._border-outline-in, .btg-button._border-round-corners, .btg-button._border-underline-from-left, .btg-button._border-underline-from-center, .btg-button._border-underline-from-right, .btg-button._border-overline-from-left, .btg-button._border-overline-from-center, .btg-button._border-overline-from-right, .btg-button._border-reveal, .btg-button._border-overline-reveal, .btg-button._border-underline-reveal {
  border: none;
}
.btg-button._border-trim:after {
  content: "";
  position: absolute;
  border-radius: var(--radius);
  border-style: var(--border-style);
  border-width: var(--border-width);
  border-color: var(--border-color);
  top: var(--border-width);
  left: var(--border-width);
  right: var(--border-width);
  bottom: var(--border-width);
  opacity: 0;
  transition-duration: 0.3s;
  transition-property: opacity;
}
.btg-button._border-trim:hover:after, .btg-button._border-trim:focus:after, .btg-button._border-trim:active:after {
  opacity: 1;
}
.btg-button._border-ripple-out:after {
  content: "";
  position: absolute;
  border-radius: var(--radius);
  border-style: none;
  border-width: var(--border-width);
  border-color: var(--border-color);
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transition: all 1s;
}
.btg-button._border-ripple-out:hover:after, .btg-button._border-ripple-out:focus:after, .btg-button._border-ripple-out:active:after {
  top: calc(var(--border-width) * 2 * -1);
  right: calc(var(--border-width) * 2 * -1);
  bottom: calc(var(--border-width) * 2 * -1);
  left: calc(var(--border-width) * 2 * -1);
  border-style: var(--border-style);
  opacity: 0;
}
.btg-button._border-ripple-in:after {
  content: "";
  position: absolute;
  border-radius: var(--radius);
  border-style: var(--border-style);
  border-width: var(--border-width);
  border-color: var(--border-color);
  top: calc(var(--border-width) * 2 * -1);
  right: calc(var(--border-width) * 2 * -1);
  bottom: calc(var(--border-width) * 2 * -1);
  left: calc(var(--border-width) * 2 * -1);
  transition: all 0.5s;
  opacity: 0;
}
.btg-button._border-ripple-in:hover:after, .btg-button._border-ripple-in:focus:after, .btg-button._border-ripple-in:active:after {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 1;
}
.btg-button._border-outline-out:after {
  content: "";
  position: absolute;
  border-radius: var(--radius);
  border-style: var(--border-style);
  border-width: var(--border-width);
  border-color: var(--border-color);
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transition: all 0.5s;
}
.btg-button._border-outline-out:hover:after, .btg-button._border-outline-out:focus:after, .btg-button._border-outline-out:active:after {
  top: calc(var(--border-width) * 2 * -1);
  right: calc(var(--border-width) * 2 * -1);
  bottom: calc(var(--border-width) * 2 * -1);
  left: calc(var(--border-width) * 2 * -1);
}
.btg-button._border-outline-in:after {
  content: "";
  position: absolute;
  border-radius: var(--radius);
  border-style: var(--border-style);
  border-width: var(--border-width);
  border-color: var(--border-color);
  top: calc(var(--border-width) * 3 * -1);
  right: calc(var(--border-width) * 3 * -1);
  bottom: calc(var(--border-width) * 3 * -1);
  left: calc(var(--border-width) * 3 * -1);
  transition: all 0.5s;
  opacity: 0;
}
.btg-button._border-outline-in:hover:after, .btg-button._border-outline-in:focus:after, .btg-button._border-outline-in:active:after {
  top: calc(var(--border-width) * 1.5 * -1);
  right: calc(var(--border-width) * 1.5 * -1);
  bottom: calc(var(--border-width) * 1.5 * -1);
  left: calc(var(--border-width) * 1.5 * -1);
  opacity: 1;
}
.btg-button._border-round-corners {
  border-radius: 0;
}
.btg-button._border-round-corners:hover, .btg-button._border-round-corners:focus, .btg-button._border-round-corners:active {
  border-radius: var(--radius);
}
.btg-button._border-underline-from-left:after, .btg-button._border-underline-from-center:after, .btg-button._border-underline-from-right:after, .btg-button._border-overline-from-left:after, .btg-button._border-overline-from-center:after, .btg-button._border-overline-from-right:after {
  content: "";
  position: absolute;
  z-index: -1;
  transition-duration: 0.3s;
  transition-timing-function: ease-out;
  border-style: var(--border-style);
  border-width: var(--border-width);
  border-color: var(--border-color);
  opacity: 0;
}
.btg-button._border-underline-from-left:hover:after, .btg-button._border-underline-from-left:focus:after, .btg-button._border-underline-from-left:active:after, .btg-button._border-underline-from-center:hover:after, .btg-button._border-underline-from-center:focus:after, .btg-button._border-underline-from-center:active:after, .btg-button._border-underline-from-right:hover:after, .btg-button._border-underline-from-right:focus:after, .btg-button._border-underline-from-right:active:after, .btg-button._border-overline-from-left:hover:after, .btg-button._border-overline-from-left:focus:after, .btg-button._border-overline-from-left:active:after, .btg-button._border-overline-from-center:hover:after, .btg-button._border-overline-from-center:focus:after, .btg-button._border-overline-from-center:active:after, .btg-button._border-overline-from-right:hover:after, .btg-button._border-overline-from-right:focus:after, .btg-button._border-overline-from-right:active:after {
  opacity: 1;
}
.btg-button._border-underline-from-left:after {
  left: 0;
  right: 100%;
  bottom: 0;
  transition-property: right;
  border-radius: 0 0 var(--radius) var(--radius);
}
.btg-button._border-underline-from-left:hover:after, .btg-button._border-underline-from-left:focus:after, .btg-button._border-underline-from-left:active:after {
  right: 0;
}
.btg-button._border-underline-from-center:after {
  left: 51%;
  right: 51%;
  bottom: 0;
  transition-property: left, right;
  border-radius: 0 0 var(--radius) var(--radius);
}
.btg-button._border-underline-from-center:hover:after, .btg-button._border-underline-from-center:focus:after, .btg-button._border-underline-from-center:active:after {
  left: 0;
  right: 0;
}
.btg-button._border-underline-from-right:after {
  left: calc(100% - var(--border-width) * 2);
  right: 0;
  bottom: 0;
  transition-property: left;
  border-radius: 0 0 var(--radius) var(--radius);
}
.btg-button._border-underline-from-right:hover:after, .btg-button._border-underline-from-right:focus:after, .btg-button._border-underline-from-right:active:after {
  left: 0;
}
.btg-button._border-overline-from-left:after {
  left: 0;
  right: 100%;
  top: 0;
  transition-property: right;
  border-radius: var(--radius) var(--radius) 0 0;
}
.btg-button._border-overline-from-left:hover:after, .btg-button._border-overline-from-left:focus:after, .btg-button._border-overline-from-left:active:after {
  right: 0;
}
.btg-button._border-overline-from-center:after {
  left: 51%;
  right: 51%;
  top: 0;
  transition-property: left, right;
  border-radius: var(--radius) var(--radius) 0 0;
}
.btg-button._border-overline-from-center:hover:after, .btg-button._border-overline-from-center:focus:after, .btg-button._border-overline-from-center:active:after {
  left: 0;
  right: 0;
}
.btg-button._border-overline-from-right:after {
  left: calc(100% - var(--border-width) * 2);
  right: 0;
  top: 0;
  transition-property: left;
  border-radius: var(--radius) var(--radius) 0 0;
}
.btg-button._border-overline-from-right:hover:after, .btg-button._border-overline-from-right:focus:after, .btg-button._border-overline-from-right:active:after {
  left: 0;
}
.btg-button._border-reveal:after, .btg-button._border-overline-reveal:after, .btg-button._border-underline-reveal:after {
  content: "";
  position: absolute;
  z-index: -1;
  transition-property: border-width;
  transition-duration: 0.1s;
  transition-timing-function: ease-out;
  border-style: var(--border-style);
  border-width: 0;
  border-color: var(--border-color);
}
.btg-button._border-reveal:hover:after, .btg-button._border-reveal:focus:after, .btg-button._border-reveal:active:after, .btg-button._border-overline-reveal:hover:after, .btg-button._border-overline-reveal:focus:after, .btg-button._border-overline-reveal:active:after, .btg-button._border-underline-reveal:hover:after, .btg-button._border-underline-reveal:focus:after, .btg-button._border-underline-reveal:active:after {
  border-width: var(--border-width);
}
.btg-button._border-reveal:after {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: var(--radius);
}
.btg-button._border-reveal:hover:after, .btg-button._border-reveal:focus:after, .btg-button._border-reveal:active:after {
  -webkit-transform: translateY(0);
  transform: translateY(0);
}
.btg-button._border-overline-reveal:after {
  left: 0;
  right: 0;
  top: 0;
  border-radius: var(--radius) var(--radius) 0 0;
}
.btg-button._border-underline-reveal:after {
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0 0 var(--radius) var(--radius);
}

.btg-button._curl-top-left, .btg-button._curl-top-right, .btg-button._curl-bottom-right, .btg-button._curl-bottom-left {
  --background-gradient: white 45%, #aaa 50%, #ccc 56%, white 80%;
}
.btg-button._curl-top-left:before, .btg-button._curl-top-right:before, .btg-button._curl-bottom-right:before, .btg-button._curl-bottom-left:before {
  pointer-events: none;
  position: absolute;
  content: "";
  height: 0;
  width: 0;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
  transition-duration: 0.3s;
  transition-property: width, height;
}
.btg-button._curl-top-left:hover:before, .btg-button._curl-top-left:focus:before, .btg-button._curl-top-left:active:before, .btg-button._curl-top-right:hover:before, .btg-button._curl-top-right:focus:before, .btg-button._curl-top-right:active:before, .btg-button._curl-bottom-right:hover:before, .btg-button._curl-bottom-right:focus:before, .btg-button._curl-bottom-right:active:before, .btg-button._curl-bottom-left:hover:before, .btg-button._curl-bottom-left:focus:before, .btg-button._curl-bottom-left:active:before {
  width: 25px;
  height: 25px;
}
.btg-button._curl-top-left:before {
  top: 0;
  left: 0;
  background: linear-gradient(135deg, var(--background-gradient));
}
.btg-button._curl-top-right:before {
  top: 0;
  right: 0;
  background: linear-gradient(225deg, var(--background-gradient));
}
.btg-button._curl-bottom-right:before {
  bottom: 0;
  right: 0;
  background: linear-gradient(315deg, var(--background-gradient));
}
.btg-button._curl-bottom-left:before {
  bottom: 0;
  left: 0;
  background: linear-gradient(45deg, var(--background-gradient));
}

.btg-button .btg-icon {
  transition-duration: 0.3s;
  transition-property: all;
  transition-timing-function: ease-out;
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-timing-function: ease-out;
  animation-timing-function: ease-out;
}
.btg-button._icon-back:hover .btg-icon, .btg-button._icon-back:focus .btg-icon, .btg-button._icon-back:active .btg-icon {
  translate: -4px;
}
.btg-button._icon-forward:hover .btg-icon, .btg-button._icon-forward:focus .btg-icon, .btg-button._icon-forward:active .btg-icon {
  translate: 4px;
}
.btg-button._icon-down:hover .btg-icon, .btg-button._icon-down:focus .btg-icon, .btg-button._icon-down:active .btg-icon {
  -webkit-animation-name: _icon-down;
  animation-name: _icon-down;
}
.btg-button._icon-up:hover .btg-icon, .btg-button._icon-up:focus .btg-icon, .btg-button._icon-up:active .btg-icon {
  -webkit-animation-name: _icon-up;
  animation-name: _icon-up;
}
.btg-button._icon-spin .btg-icon {
  transition-duration: 1s;
}
.btg-button._icon-spin:hover .btg-icon, .btg-button._icon-spin:focus .btg-icon, .btg-button._icon-spin:active .btg-icon {
  rotate: 360deg;
}
.btg-button._icon-drop:hover .btg-icon, .btg-button._icon-drop:focus .btg-icon, .btg-button._icon-drop:active .btg-icon {
  opacity: 0;
  -webkit-animation-name: _icon-drop;
  animation-name: _icon-drop;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
  animation-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
}
.btg-button._icon-fade:hover .btg-icon, .btg-button._icon-fade:focus .btg-icon, .btg-button._icon-fade:active .btg-icon {
  color: currentColor;
}
.btg-button._icon-float-away .btg-icon {
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.btg-button._icon-float-away:hover .btg-icon, .btg-button._icon-float-away:focus .btg-icon, .btg-button._icon-float-away:active .btg-icon {
  -webkit-animation-name: _icon-float-away;
  animation-name: _icon-float-away;
}
.btg-button._icon-sink-away .btg-icon {
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.btg-button._icon-sink-away:hover .btg-icon, .btg-button._icon-sink-away:focus .btg-icon, .btg-button._icon-sink-away:active .btg-icon {
  -webkit-animation-name: _icon-sink-away;
  animation-name: _icon-sink-away;
}
.btg-button._icon-grow:hover .btg-icon, .btg-button._icon-grow:focus .btg-icon, .btg-button._icon-grow:active .btg-icon {
  scale: 1.3;
}
.btg-button._icon-shrink:hover .btg-icon, .btg-button._icon-shrink:focus .btg-icon, .btg-button._icon-shrink:active .btg-icon {
  scale: 0.8;
}
.btg-button._icon-pulse:hover .btg-icon, .btg-button._icon-pulse:focus .btg-icon, .btg-button._icon-pulse:active .btg-icon, .btg-button._icon-pulse-grow:hover .btg-icon, .btg-button._icon-pulse-grow:focus .btg-icon, .btg-button._icon-pulse-grow:active .btg-icon, .btg-button._icon-pulse-shrink:hover .btg-icon, .btg-button._icon-pulse-shrink:focus .btg-icon, .btg-button._icon-pulse-shrink:active .btg-icon, .btg-button._icon-pop:hover .btg-icon, .btg-button._icon-pop:focus .btg-icon, .btg-button._icon-pop:active .btg-icon {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.btg-button._icon-pulse:hover .btg-icon, .btg-button._icon-pulse:focus .btg-icon, .btg-button._icon-pulse:active .btg-icon {
  -webkit-animation-name: _pulse;
  animation-name: _pulse;
}
.btg-button._icon-pulse-grow:hover .btg-icon, .btg-button._icon-pulse-grow:focus .btg-icon, .btg-button._icon-pulse-grow:active .btg-icon {
  -webkit-animation-name: _pulse-grow;
  animation-name: _pulse-grow;
  -webkit-animation-direction: alternate;
  animation-direction: alternate;
}
.btg-button._icon-pulse-shrink:hover .btg-icon, .btg-button._icon-pulse-shrink:focus .btg-icon, .btg-button._icon-pulse-shrink:active .btg-icon {
  -webkit-animation-name: _pulse-shrink;
  animation-name: _pulse-shrink;
  -webkit-animation-direction: alternate;
  animation-direction: alternate;
}
.btg-button._icon-push:hover .btg-icon, .btg-button._icon-push:focus .btg-icon, .btg-button._icon-push:active .btg-icon {
  -webkit-animation-name: _push;
  animation-name: _push;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-direction: alternate;
  animation-direction: alternate;
}
.btg-button._icon-pop:hover .btg-icon, .btg-button._icon-pop:focus .btg-icon, .btg-button._icon-pop:active .btg-icon {
  -webkit-animation-name: _pop;
  animation-name: _pop;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -webkit-animation-direction: alternate;
  animation-direction: alternate;
}
.btg-button._icon-bounce:hover .btg-icon, .btg-button._icon-bounce:focus .btg-icon, .btg-button._icon-bounce:active .btg-icon {
  scale: 1.5;
  transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
}
.btg-button._icon-rotate:hover .btg-icon, .btg-button._icon-rotate:focus .btg-icon, .btg-button._icon-rotate:active .btg-icon {
  rotate: 20deg;
}
.btg-button._icon-grow-rotate:hover .btg-icon, .btg-button._icon-grow-rotate:focus .btg-icon, .btg-button._icon-grow-rotate:active .btg-icon {
  rotate: 12deg;
  scale: 1.5;
}
.btg-button._icon-float:hover .btg-icon, .btg-button._icon-float:focus .btg-icon, .btg-button._icon-float:active .btg-icon {
  translate: 0 -4px;
}
.btg-button._icon-sink:hover .btg-icon, .btg-button._icon-sink:focus .btg-icon, .btg-button._icon-sink:active .btg-icon {
  translate: 0 4px;
}
.btg-button._icon-bob:hover .btg-icon, .btg-button._icon-bob:focus .btg-icon, .btg-button._icon-bob:active .btg-icon, .btg-button._icon-hang:hover .btg-icon, .btg-button._icon-hang:focus .btg-icon, .btg-button._icon-hang:active .btg-icon {
  -webkit-animation-duration: 0.3s, 1.5s;
  animation-duration: 0.3s, 1.5s;
  -webkit-animation-delay: 0s, 0.3s;
  animation-delay: 0s, 0.3s;
  -webkit-animation-timing-function: ease-out, ease-in-out;
  animation-timing-function: ease-out, ease-in-out;
  -webkit-animation-iteration-count: 1, infinite;
  animation-iteration-count: 1, infinite;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-direction: normal, alternate;
  animation-direction: normal, alternate;
}
.btg-button._icon-bob:hover .btg-icon, .btg-button._icon-bob:focus .btg-icon, .btg-button._icon-bob:active .btg-icon {
  -webkit-animation-name: _bob-float, _bob;
  animation-name: _bob-float, _bob;
}
.btg-button._icon-hang:hover .btg-icon, .btg-button._icon-hang:focus .btg-icon, .btg-button._icon-hang:active .btg-icon {
  -webkit-animation-name: _hang-sink, _hang;
  animation-name: _hang-sink, _hang;
}
.btg-button._icon-wobble-horizontal:hover .btg-icon, .btg-button._icon-wobble-horizontal:focus .btg-icon, .btg-button._icon-wobble-horizontal:active .btg-icon, .btg-button._icon-wobble-vertical:hover .btg-icon, .btg-button._icon-wobble-vertical:focus .btg-icon, .btg-button._icon-wobble-vertical:active .btg-icon {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}
.btg-button._icon-wobble-horizontal:hover .btg-icon, .btg-button._icon-wobble-horizontal:focus .btg-icon, .btg-button._icon-wobble-horizontal:active .btg-icon {
  -webkit-animation-name: _wobble-horizontal;
  animation-name: _wobble-horizontal;
}
.btg-button._icon-wobble-vertical:hover .btg-icon, .btg-button._icon-wobble-vertical:focus .btg-icon, .btg-button._icon-wobble-vertical:active .btg-icon {
  -webkit-animation-name: _wobble-vertical;
  animation-name: _wobble-vertical;
}
.btg-button._icon-buzz:hover .btg-icon, .btg-button._icon-buzz:focus .btg-icon, .btg-button._icon-buzz:active .btg-icon {
  -webkit-animation-name: _buzz;
  animation-name: _buzz;
  -webkit-animation-duration: 0.15s;
  animation-duration: 0.15s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.btg-button._icon-buzz-out:hover .btg-icon, .btg-button._icon-buzz-out:focus .btg-icon, .btg-button._icon-buzz-out:active .btg-icon {
  -webkit-animation-name: _buzz-out;
  animation-name: _buzz-out;
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

@-webkit-keyframes btn-bounce {
  from, 20%, 53%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0) scaleY(1.1);
    transform: translate3d(0, -30px, 0) scaleY(1.1);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0) scaleY(1.05);
    transform: translate3d(0, -15px, 0) scaleY(1.05);
  }
  80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0) scaleY(0.95);
    transform: translate3d(0, 0, 0) scaleY(0.95);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0) scaleY(1.02);
    transform: translate3d(0, -4px, 0) scaleY(1.02);
  }
}

@keyframes btn-bounce {
  from, 20%, 53%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0) scaleY(1.1);
    transform: translate3d(0, -30px, 0) scaleY(1.1);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0) scaleY(1.05);
    transform: translate3d(0, -15px, 0) scaleY(1.05);
  }
  80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0) scaleY(0.95);
    transform: translate3d(0, 0, 0) scaleY(0.95);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0) scaleY(1.02);
    transform: translate3d(0, -4px, 0) scaleY(1.02);
  }
}
@-webkit-keyframes btn-flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
@keyframes btn-flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
@-webkit-keyframes btn-headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg);
  }
  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg);
  }
  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg);
  }
  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg);
  }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes btn-headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg);
  }
  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg);
  }
  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg);
  }
  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg);
  }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes btn-jello {
  from, 11.1%, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
@keyframes btn-jello {
  from, 11.1%, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
@-webkit-keyframes btn-heartBeat {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  14% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }
  28% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  42% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }
  70% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes btn-heartBeat {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  14% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }
  28% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  42% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }
  70% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@-webkit-keyframes btn-pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes btn-pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@-webkit-keyframes btn-rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes btn-rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@-webkit-keyframes btn-shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@keyframes btn-shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@-webkit-keyframes btn-shakeX {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@keyframes btn-shakeX {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@-webkit-keyframes btn-shakeY {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
}
@keyframes btn-shakeY {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
}
@-webkit-keyframes btn-swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
@keyframes btn-swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
@-webkit-keyframes btn-tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes btn-tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@-webkit-keyframes btn-wobble {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes btn-wobble {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes _pulse {
  25% {
    scale: 1.1;
  }
  75% {
    scale: 0.9;
  }
}
@keyframes _pulse {
  25% {
    scale: 1.1;
  }
  75% {
    scale: 0.9;
  }
}
@-webkit-keyframes _pulse-grow {
  to {
    scale: 1.1;
  }
}
@keyframes _pulse-grow {
  to {
    scale: 1.1;
  }
}
@-webkit-keyframes _pulse-shrink {
  to {
    scale: 0.9;
  }
}
@keyframes _pulse-shrink {
  to {
    scale: 0.9;
  }
}
@-webkit-keyframes _push {
  50% {
    scale: 0.8;
  }
  100% {
    scale: 1;
  }
}
@keyframes _push {
  50% {
    scale: 0.8;
  }
  100% {
    scale: 1;
  }
}
@-webkit-keyframes _pop {
  50% {
    scale: 1.2;
  }
}
@keyframes _pop {
  50% {
    scale: 1.2;
  }
}
@-webkit-keyframes _bob {
  0% {
    translate: 0 -8px;
  }
  50% {
    translate: 0 -4px;
  }
  100% {
    translate: 0 -8px;
  }
}
@keyframes _bob {
  0% {
    translate: 0 -8px;
  }
  50% {
    translate: 0 -4px;
  }
  100% {
    translate: 0 -8px;
  }
}
@-webkit-keyframes _bob-float {
  100% {
    translate: 0 -8px;
  }
}
@keyframes _bob-float {
  100% {
    translate: 0 -8px;
  }
}
@-webkit-keyframes _hang {
  0% {
    translate: 0 8px;
  }
  50% {
    translate: 0 4px;
  }
  100% {
    translate: 0 8px;
  }
}
@keyframes _hang {
  0% {
    translate: 0 8px;
  }
  50% {
    translate: 0 4px;
  }
  100% {
    translate: 0 8px;
  }
}
@-webkit-keyframes _hang-sink {
  100% {
    translate: 0 8px;
  }
}
@keyframes _hang-sink {
  100% {
    translate: 0 8px;
  }
}
@-webkit-keyframes _wobble-horizontal {
  16.65% {
    translate: 8px;
  }
  33.3% {
    translate: -6px;
  }
  49.95% {
    translate: 4px;
  }
  66.6% {
    translate: -2px;
  }
  83.25% {
    translate: 1px;
  }
  100% {
    translate: 0;
  }
}
@keyframes _wobble-horizontal {
  16.65% {
    translate: 8px;
  }
  33.3% {
    translate: -6px;
  }
  49.95% {
    translate: 4px;
  }
  66.6% {
    translate: -2px;
  }
  83.25% {
    translate: 1px;
  }
  100% {
    translate: 0;
  }
}
@-webkit-keyframes _wobble-vertical {
  16.65% {
    translate: 0 8px;
  }
  33.3% {
    translate: 0 -6px;
  }
  49.95% {
    translate: 0 4px;
  }
  66.6% {
    translate: 0 -2px;
  }
  83.25% {
    translate: 0 1px;
  }
  100% {
    translate: 0 0;
  }
}
@keyframes _wobble-vertical {
  16.65% {
    translate: 0 8px;
  }
  33.3% {
    translate: 0 -6px;
  }
  49.95% {
    translate: 0 4px;
  }
  66.6% {
    translate: 0 -2px;
  }
  83.25% {
    translate: 0 1px;
  }
  100% {
    translate: 0 0;
  }
}
@-webkit-keyframes _wobble-to-bottom-right {
  16.65% {
    translate: 8px 8px;
  }
  33.3% {
    translate: -6px -6px;
  }
  49.95% {
    translate: 4px 4px;
  }
  66.6% {
    translate: -2px -2px;
  }
  83.25% {
    translate: 1px 1px;
  }
  100% {
    translate: 0 0;
  }
}
@keyframes _wobble-to-bottom-right {
  16.65% {
    translate: 8px 8px;
  }
  33.3% {
    translate: -6px -6px;
  }
  49.95% {
    translate: 4px 4px;
  }
  66.6% {
    translate: -2px -2px;
  }
  83.25% {
    translate: 1px 1px;
  }
  100% {
    translate: 0 0;
  }
}
@-webkit-keyframes _wobble-to-top-right {
  16.65% {
    translate: 8px -8px;
  }
  33.3% {
    translate: -6px 6px;
  }
  49.95% {
    translate: 4px -4px;
  }
  66.6% {
    translate: -2px 2px;
  }
  83.25% {
    translate: 1px -1px;
  }
  100% {
    translate: 0 0;
  }
}
@keyframes _wobble-to-top-right {
  16.65% {
    translate: 8px -8px;
  }
  33.3% {
    translate: -6px 6px;
  }
  49.95% {
    translate: 4px -4px;
  }
  66.6% {
    translate: -2px 2px;
  }
  83.25% {
    translate: 1px -1px;
  }
  100% {
    translate: 0 0;
  }
}
@-webkit-keyframes _wobble-top {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }
  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }
  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }
  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }
  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }
  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}
@keyframes _wobble-top {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }
  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }
  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }
  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }
  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }
  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}
@-webkit-keyframes _wobble-bottom {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }
  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }
  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }
  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }
  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }
  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}
@keyframes _wobble-bottom {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }
  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }
  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }
  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }
  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }
  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}
@-webkit-keyframes _wobble-skew {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }
  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }
  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }
  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }
  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }
  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}
@keyframes _wobble-skew {
  16.65% {
    -webkit-transform: skew(-12deg);
    transform: skew(-12deg);
  }
  33.3% {
    -webkit-transform: skew(10deg);
    transform: skew(10deg);
  }
  49.95% {
    -webkit-transform: skew(-6deg);
    transform: skew(-6deg);
  }
  66.6% {
    -webkit-transform: skew(4deg);
    transform: skew(4deg);
  }
  83.25% {
    -webkit-transform: skew(-2deg);
    transform: skew(-2deg);
  }
  100% {
    -webkit-transform: skew(0);
    transform: skew(0);
  }
}
@-webkit-keyframes _buzz {
  50% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }
  100% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }
}
@keyframes _buzz {
  50% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }
  100% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }
}
@-webkit-keyframes _buzz-out {
  10% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }
  20% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }
  30% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }
  40% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }
  50% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }
  60% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }
  70% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }
  80% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }
  90% {
    -webkit-transform: translateX(1px) rotate(0);
    transform: translateX(1px) rotate(0);
  }
  100% {
    -webkit-transform: translateX(-1px) rotate(0);
    transform: translateX(-1px) rotate(0);
  }
}
@keyframes _buzz-out {
  10% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }
  20% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }
  30% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }
  40% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }
  50% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }
  60% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }
  70% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }
  80% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }
  90% {
    -webkit-transform: translateX(1px) rotate(0);
    transform: translateX(1px) rotate(0);
  }
  100% {
    -webkit-transform: translateX(-1px) rotate(0);
    transform: translateX(-1px) rotate(0);
  }
}
@-webkit-keyframes _icon-down {
  0%, 50%, 100% {
    translate: 0 0;
  }
  25%, 75% {
    translate: 0 6px;
  }
}
@keyframes _icon-down {
  0%, 50%, 100% {
    translate: 0 0;
  }
  25%, 75% {
    translate: 0 6px;
  }
}
@-webkit-keyframes _icon-up {
  0%, 50%, 100% {
    translate: 0 0;
  }
  25%, 75% {
    translate: 0 -6px;
  }
}
@keyframes _icon-up {
  0%, 50%, 100% {
    translate: 0 0;
  }
  25%, 75% {
    translate: 0 -6px;
  }
}
@-webkit-keyframes _icon-drop {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
    translate: 0 -100%;
  }
  51%, 100% {
    opacity: 1;
  }
}
@keyframes _icon-drop {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
    translate: 0 -100%;
  }
  51%, 100% {
    opacity: 1;
  }
}
@-webkit-keyframes _icon-float-away {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    translate: 0 -1em;
  }
}
@keyframes _icon-float-away {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    translate: 0 -1em;
  }
}
@-webkit-keyframes _icon-sink-away {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    translate: 0 1em;
  }
}
@keyframes _icon-sink-away {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    translate: 0 1em;
  }
}
@-webkit-keyframes _back-pulse {
  50% {
    opacity: 0.75;
  }
}
@keyframes _back-pulse {
  50% {
    opacity: 0.75;
  }
}
