<?php

/**
 * Class Settings_Helper
 *
 * This class contains helper methods for retrieving menu item types, share services,
 * and translation options.
 *
 * @package    ButtonGenerator
 * @subpackage Admin
 * <AUTHOR> Lobov <<EMAIL>>, Wow-Company
 * @copyright  2024 Dmytro Lobov
 * @license    GPL-2.0+
 */

namespace ButtonGenerator;

defined( 'ABSPATH' ) || exit;

class Settings_Helper {

	public static function item_type(): array {
		return [
			'link'          => __( 'Link', 'button-generation' ),
			'login'         => __( 'Login', 'button-generation' ),
			'logout'        => __( 'Logout', 'button-generation' ),
			'lostpassword'  => __( 'Lostpassword', 'button-generation' ),
			'register'      => __( 'Register', 'button-generation' ),
		];
	}


	public static function share_services(): array {
		return [
			'Facebook'         => __( 'Facebook', 'button-generation' ),
			'Twitter'          => __( 'Twitter', 'button-generation' ),
			'Linkedin'         => __( 'Linkedin', 'button-generation' ),
			'Pinterest'        => __( 'Pinterest', 'button-generation' ),
			'stumbleupon'      => __( 'StumbleUpon', 'button-generation' ),
			'buffer'           => __( 'Buffer', 'button-generation' ),
			'email'            => __( 'Email', 'button-generation' ),
			'xing'             => __( 'XING', 'button-generation' ),
			'myspace'          => __( 'Myspace', 'button-generation' ),
			'weibo'            => __( 'Weibo', 'button-generation' ),
			'reddit'           => __( 'Reddit', 'button-generation' ),
			'tumblr'           => __( 'Tumblr', 'button-generation' ),
			'blogger'          => __( 'Blogger', 'button-generation' ),
			'livejournal'      => __( 'LiveJournal', 'button-generation' ),
			'pocket'           => __( 'Pocket', 'button-generation' ),
			'telegram'         => __( 'Telegram', 'button-generation' ),
			'skype'            => __( 'Skype', 'button-generation' ),
			'draugiem'         => __( 'Draugiem', 'button-generation' ),
			'whatsapp'         => __( 'Whatsapp', 'button-generation' ),
			'diaspora'         => __( 'Diaspora', 'button-generation' ),
			'digg'             => __( 'Digg', 'button-generation' ),
			'douban'           => __( 'Douban', 'button-generation' ),
			'evernote'         => __( 'Evernote', 'button-generation' ),
			'flipboard'        => __( 'Flipboard', 'button-generation' ),
			'hacker-news'      => __( 'Hacker News', 'button-generation' ),
			'instapaper'       => __( 'Instapaper', 'button-generation' ),
			'line'             => __( 'Line', 'button-generation' ),
			'qzone'            => __( 'Qzone', 'button-generation' ),
			'renren'           => __( 'Renren', 'button-generation' ),
		];
	}

	public static function gtranslate(): array {
		return [
			'af'  => __( 'Afrikaans', 'button-generation' ),
			'sq'  => __( 'Albanian', 'button-generation' ),
			'am'  => __( 'Amharic', 'button-generation' ),
			'ar'  => __( 'Arabic', 'button-generation' ),
			'hy'  => __( 'Armenian', 'button-generation' ),
			'az'  => __( 'Azerbaijani', 'button-generation' ),
			'eu'  => __( 'Basque', 'button-generation' ),
			'be'  => __( 'Belarusian', 'button-generation' ),
			'bn'  => __( 'Bengali', 'button-generation' ),
			'bs'  => __( 'Bosnian', 'button-generation' ),
			'bg'  => __( 'Bulgarian', 'button-generation' ),
			'ca'  => __( 'Catalan', 'button-generation' ),
			'ceb' => __( 'Cebuano', 'button-generation' ),
			'ny'  => __( 'Chichewa', 'button-generation' ),
			'co'  => __( 'Corsican', 'button-generation' ),
			'hr'  => __( 'Croatian', 'button-generation' ),
			'cs'  => __( 'Czech', 'button-generation' ),
			'da'  => __( 'Danish', 'button-generation' ),
			'nl'  => __( 'Dutch', 'button-generation' ),
			'en'  => __( 'English', 'button-generation' ),
			'eo'  => __( 'Esperanto', 'button-generation' ),
			'et'  => __( 'Estonian', 'button-generation' ),
			'tl'  => __( 'Filipino', 'button-generation' ),
			'fi'  => __( 'Finnish', 'button-generation' ),
			'fr'  => __( 'French', 'button-generation' ),
			'fy'  => __( 'Frisian', 'button-generation' ),
			'gl'  => __( 'Galician', 'button-generation' ),
			'ka'  => __( 'Georgian', 'button-generation' ),
			'de'  => __( 'German', 'button-generation' ),
			'el'  => __( 'Greek', 'button-generation' ),
			'gu'  => __( 'Gujarati', 'button-generation' ),
			'ht'  => __( 'Haitian Creole', 'button-generation' ),
			'ha'  => __( 'Hausa', 'button-generation' ),
			'haw' => __( 'Hawaiian', 'button-generation' ),
			'iw'  => __( 'Hebrew', 'button-generation' ),
			'hi'  => __( 'Hindi', 'button-generation' ),
			'hmn' => __( 'Hmong', 'button-generation' ),
			'hu'  => __( 'Hungarian', 'button-generation' ),
			'is'  => __( 'Icelandic', 'button-generation' ),
			'ig'  => __( 'Igbo', 'button-generation' ),
			'id'  => __( 'Indonesian', 'button-generation' ),
			'ga'  => __( 'Irish', 'button-generation' ),
			'it'  => __( 'Italian', 'button-generation' ),
			'ja'  => __( 'Japanese', 'button-generation' ),
			'jw'  => __( 'Javanese', 'button-generation' ),
			'kn'  => __( 'Kannada', 'button-generation' ),
			'kk'  => __( 'Kazakh', 'button-generation' ),
			'km'  => __( 'Khmer', 'button-generation' ),
			'ko'  => __( 'Korean', 'button-generation' ),
			'ku'  => __( 'Kurdish (Kurmanji)', 'button-generation' ),
			'ky'  => __( 'Kyrgyz', 'button-generation' ),
			'lo'  => __( 'Lao', 'button-generation' ),
			'la'  => __( 'Latin', 'button-generation' ),
			'lv'  => __( 'Latvian', 'button-generation' ),
			'lb'  => __( 'Luxembourgish', 'button-generation' ),
			'mk'  => __( 'Macedonian', 'button-generation' ),
			'mg'  => __( 'Malagasy', 'button-generation' ),
			'ms'  => __( 'Malay', 'button-generation' ),
			'ml'  => __( 'Malayalam', 'button-generation' ),
			'mt'  => __( 'Maltese', 'button-generation' ),
			'mi'  => __( 'Maori', 'button-generation' ),
			'mr'  => __( 'Marathi', 'button-generation' ),
			'mn'  => __( 'Mongolian', 'button-generation' ),
			'my'  => __( 'Myanmar (Burmese)', 'button-generation' ),
			'ne'  => __( 'Nepali', 'button-generation' ),
			'no'  => __( 'Norwegian', 'button-generation' ),
			'ps'  => __( 'Pashto', 'button-generation' ),
			'fa'  => __( 'Persian', 'button-generation' ),
			'pl'  => __( 'Polish', 'button-generation' ),
			'pt'  => __( 'Portuguese', 'button-generation' ),
			'pa'  => __( 'Punjabi', 'button-generation' ),
			'ro'  => __( 'Romanian', 'button-generation' ),
			'ru'  => __( 'Russian', 'button-generation' ),
			'sm'  => __( 'Samoan', 'button-generation' ),
			'gd'  => __( 'Scottish Gaelic', 'button-generation' ),
			'sr'  => __( 'Serbian', 'button-generation' ),
			'st'  => __( 'Sesotho', 'button-generation' ),
			'sn'  => __( 'Shona', 'button-generation' ),
			'sd'  => __( 'Sindhi', 'button-generation' ),
			'si'  => __( 'Sinhala', 'button-generation' ),
			'sk'  => __( 'Slovak', 'button-generation' ),
			'sl'  => __( 'Slovenian', 'button-generation' ),
			'so'  => __( 'Somali', 'button-generation' ),
			'es'  => __( 'Spanish', 'button-generation' ),
			'su'  => __( 'Sudanese', 'button-generation' ),
			'sw'  => __( 'Swahili', 'button-generation' ),
			'sv'  => __( 'Swedish', 'button-generation' ),
			'tg'  => __( 'Tajik', 'button-generation' ),
			'ta'  => __( 'Tamil', 'button-generation' ),
			'te'  => __( 'Telugu', 'button-generation' ),
			'th'  => __( 'Thai', 'button-generation' ),
			'tr'  => __( 'Turkish', 'button-generation' ),
			'uk'  => __( 'Ukrainian', 'button-generation' ),
			'ur'  => __( 'Urdu', 'button-generation' ),
			'uz'  => __( 'Uzbek', 'button-generation' ),
			'vi'  => __( 'Vietnamese', 'button-generation' ),
			'cy'  => __( 'Welsh', 'button-generation' ),
			'xh'  => __( 'Xhosa', 'button-generation' ),
			'yi'  => __( 'Yiddish', 'button-generation' ),
			'yo'  => __( 'Yoruba', 'button-generation' ),
			'zu'  => __( 'Zulu', 'button-generation' ),
		];
	}

	public static function languages(): array {
		return [
			'af'             => 'Afrikaans',
			'ar'             => 'العربية',
			'ary'            => 'العربية المغربية',
			'as'             => 'অসমীয়া',
			'az'             => 'Azərbaycan dili',
			'azb'            => 'گؤنئی آذربایجان',
			'bel'            => 'Беларуская мова',
			'bg_BG'          => 'Български',
			'bn_BD'          => 'বাংলা',
			'bo'             => 'བོད་ཡིག',
			'bs_BA'          => 'Bosanski',
			'ca'             => 'Català',
			'ceb'            => 'Cebuano',
			'cs_CZ'          => 'Čeština',
			'cy'             => 'Cymraeg',
			'da_DK'          => 'Dansk',
			'de_DE'          => 'Deutsch',
			'de_CH_informal' => 'Deutsch (Schweiz, Du)',
			'de_CH'          => 'Deutsch (Schweiz)',
			'de_DE_formal'   => 'Deutsch (Sie)',
			'de_AT'          => 'Deutsch (Österreich)',
			'dzo'            => 'རྫོང་ཁ',
			'el'             => 'Ελληνικά',
			'en_US'          => 'English (United States)',
			'en_GB'          => 'English (UK)',
			'en_AU'          => 'English (Australia)',
			'en_NZ'          => 'English (New Zealand)',
			'en_CA'          => 'English (Canada)',
			'en_ZA'          => 'English (South Africa)',
			'eo'             => 'Esperanto',
			'es_ES'          => 'Español',
			'es_VE'          => 'Español de Venezuela',
			'es_GT'          => 'Español de Guatemala',
			'es_CR'          => 'Español de Costa Rica',
			'es_MX'          => 'Español de México',
			'es_CO'          => 'Español de Colombia',
			'es_PE'          => 'Español de Perú',
			'es_CL'          => 'Español de Chile',
			'es_AR'          => 'Español de Argentina',
			'et'             => 'Eesti',
			'eu'             => 'Euskara',
			'fa_IR'          => 'فارسی',
			'fi'             => 'Suomi',
			'fr_FR'          => 'Français',
			'fr_CA'          => 'Français du Canada',
			'fr_BE'          => 'Français de Belgique',
			'fur'            => 'Friulian',
			'gd'             => 'Gàidhlig',
			'gl_ES'          => 'Galego',
			'gu'             => 'ગુજરાતી',
			'haz'            => 'هزاره گی',
			'he_IL'          => 'עִבְרִית',
			'hi_IN'          => 'हिन्दी',
			'hr'             => 'Hrvatski',
			'hu_HU'          => 'Magyar',
			'hy'             => 'Հայերեն',
			'id_ID'          => 'Bahasa Indonesia',
			'is_IS'          => 'Íslenska',
			'it_IT'          => 'Italiano',
			'ja'             => '日本語',
			'jv_ID'          => 'Basa Jawa',
			'ka_GE'          => 'ქართული',
			'kab'            => 'Taqbaylit',
			'kk'             => 'Қазақ тілі',
			'km'             => 'ភាសាខ្មែរ',
			'kn'             => 'ಕನ್ನಡ',
			'ko_KR'          => '한국어',
			'ckb'            => 'كوردی‎',
			'lo'             => 'ພາສາລາວ',
			'lt_LT'          => 'Lietuvių kalba',
			'lv'             => 'Latviešu valoda',
			'mk_MK'          => 'Македонски јазик',
			'ml_IN'          => 'മലയാളം',
			'mn'             => 'Монгол',
			'mr'             => 'मराठी',
			'ms_MY'          => 'Bahasa Melayu',
			'my_MM'          => 'ဗမာစာ',
			'nb_NO'          => 'Norsk bokmål',
			'ne_NP'          => 'नेपाली',
			'nl_NL'          => 'Nederlands',
			'nl_NL_formal'   => 'Nederlands (Formeel)',
			'nl_BE'          => 'Nederlands (België)',
			'nn_NO'          => 'Norsk nynorsk',
			'oci'            => 'Occitan',
			'pa_IN'          => 'ਪੰਜਾਬੀ',
			'pl_PL'          => 'Polski',
			'ps'             => 'پښتو',
			'pt_PT'          => 'Português',
			'pt_AO'          => 'Português de Angola',
			'pt_PT_ao90'     => 'Português (AO90)',
			'pt_BR'          => 'Português do Brasil',
			'rhg'            => 'Ruáinga',
			'ro_RO'          => 'Română',
			'ru_RU'          => 'Русский',
			'sah'            => 'Сахалыы',
			'si_LK'          => 'සිංහල',
			'sk_SK'          => 'Slovenčina',
			'skr'            => 'سرائیکی',
			'sl_SI'          => 'Slovenščina',
			'sq'             => 'Shqip',
			'sr_RS'          => 'Српски језик',
			'sv_SE'          => 'Svenska',
			'szl'            => 'Ślōnskŏ gŏdka',
			'ta_IN'          => 'தமிழ்',
			'te'             => 'తెలుగు',
			'th'             => 'ไทย',
			'tl'             => 'Tagalog',
			'tr_TR'          => 'Türkçe',
			'tt_RU'          => 'Татар теле',
			'tah'            => 'Reo Tahiti',
			'ug_CN'          => 'ئۇيغۇرچە',
			'uk'             => 'Українська',
			'ur'             => 'اردو',
			'uz_UZ'          => 'O‘zbekcha',
			'vi'             => 'Tiếng Việt',
			'zh_CN'          => '简体中文',
			'zh_TW'          => '繁體中文',
			'zh_HK'          => '香港中文版',
		];
	}

	public static function hover_effect(): array {
		return [
			'none'                    => __( 'None', 'button-generation' ),
			'_grow'                   => __( 'Grow', 'button-generation' ),
			'_shrink'                 => __( 'Shrink', 'button-generation' ),
			'_pulse'                  => __( 'Pulse', 'button-generation' ),
			'_pulse-grow'             => __( 'Pulse Grow', 'button-generation' ),
			'_pulse-shrink'           => __( 'Pulse Shrink', 'button-generation' ),
			'_push'                   => __( 'Push', 'button-generation' ),
			'_pop'                    => __( 'Pop', 'button-generation' ),
			'_bounce-in'              => __( 'Bounce In', 'button-generation' ),
			'_bounce-out'             => __( 'Bounce Out', 'button-generation' ),
			'_rotate'                 => __( 'Rotate', 'button-generation' ),
			'_grow-rotate'            => __( 'Grow Rotate', 'button-generation' ),
			'_float'                  => __( 'Float', 'button-generation' ),
			'_sink'                   => __( 'Sink', 'button-generation' ),
			'_bob'                    => __( 'Bob', 'button-generation' ),
			'_hang'                   => __( 'Hang', 'button-generation' ),
			'_skew'                   => __( 'Skew', 'button-generation' ),
			'_skew-forward'           => __( 'Skew Forward', 'button-generation' ),
			'_skew-backward'          => __( 'Skew Backward', 'button-generation' ),
			'_wobble-horizontal'      => __( 'Wobble Horizontal', 'button-generation' ),
			'_wobble-vertical'        => __( 'Wobble Vertical', 'button-generation' ),
			'_wobble-to-bottom-right' => __( 'Wobble To Bottom Right', 'button-generation' ),
			'_wobble-to-top-right'    => __( 'Wobble To Top Right', 'button-generation' ),
			'_wobble-top'             => __( 'Wobble Top', 'button-generation' ),
			'_wobble-bottom'          => __( 'Wobble Bottom', 'button-generation' ),
			'_wobble-skew'            => __( 'Wobble Skew', 'button-generation' ),
			'_buzz'                   => __( 'Buzz', 'button-generation' ),
			'_buzz-out'               => __( 'Buzz Out', 'button-generation' ),
			'_forward'                => __( 'Forward', 'button-generation' ),
			'_backward'               => __( 'Backward', 'button-generation' ),
		];

	}

	public static function background_effect(): array {
		return [
			'none'                    => __( 'None', 'button-generation' ),
			'background_start'        => __( 'Background Transitions', 'button-generation' ),
			'_back-pulse'             => __( 'Back Pulse', 'button-generation' ),
			'_sweep-to-right'         => __( 'Sweep To Right', 'button-generation' ),
			'_sweep-to-left'          => __( 'Sweep To Left', 'button-generation' ),
			'_sweep-to-bottom'        => __( 'Sweep To Bottom', 'button-generation' ),
			'_sweep-to-top'           => __( 'Sweep To Top', 'button-generation' ),
			'_bounce-to-right'        => __( 'Bounce To Right', 'button-generation' ),
			'_bounce-to-left'         => __( 'Bounce To Left', 'button-generation' ),
			'_bounce-to-bottom'       => __( 'Bounce To Bottom', 'button-generation' ),
			'_bounce-to-top'          => __( 'Bounce To Top', 'button-generation' ),
			'_radial-out'             => __( 'Radial Out', 'button-generation' ),
			'_radial-in'              => __( 'Radial In', 'button-generation' ),
			'_rectangle-out'          => __( 'Rectangle Out', 'button-generation' ),
			'_rectangle-in'           => __( 'Rectangle In', 'button-generation' ),
			'_shutter-out-horizontal' => __( 'Shutter Out Horizontal', 'button-generation' ),
			'_shutter-in-horizontal'  => __( 'Shutter In Horizontal', 'button-generation' ),
			'_shutter-out-vertical'   => __( 'Shutter Out Vertical', 'button-generation' ),
			'_shutter-in-vertical'    => __( 'Shutter In Vertical', 'button-generation' ),
			'background_end'          => '',
			'group_start'             => __( 'Curls', 'button-generation' ),
			'_curl-top-left'          => __( 'Curl Top Left', 'button-generation' ),
			'_curl-top-right'         => __( 'Curl Top Right', 'button-generation' ),
			'_curl-bottom-right'      => __( 'Curl Bottom Right', 'button-generation' ),
			'_curl-bottom-left'       => __( 'Curl Bottom Left', 'button-generation' ),
			'group_end'               => '',
		];

	}

	public static function icon_effect(): array {
		return [
			'none'                    => __( 'None', 'button-generation' ),
			'_icon-back'              => __( 'Icon Back', 'button-generation' ),
			'_icon-forward'           => __( 'Icon Forward', 'button-generation' ),
			'_icon-down'              => __( 'Icon Down', 'button-generation' ),
			'_icon-up'                => __( 'Icon Up', 'button-generation' ),
			'_icon-spin'              => __( 'Icon Spin', 'button-generation' ),
			'_icon-drop'              => __( 'Icon Drop', 'button-generation' ),
			'_icon-fade'              => __( 'Icon Fade', 'button-generation' ),
			'_icon-float-away'        => __( 'Icon Float Away', 'button-generation' ),
			'_icon-sink-away'         => __( 'Icon Sink Away', 'button-generation' ),
			'_icon-grow'              => __( 'Icon Grow', 'button-generation' ),
			'_icon-shrink'            => __( 'Icon Shrink', 'button-generation' ),
			'_icon-pulse'             => __( 'Icon Pulse', 'button-generation' ),
			'_icon-pulse-grow'        => __( 'Icon Pulse Grow', 'button-generation' ),
			'_icon-pulse-shrink'      => __( 'Icon Pulse Shrink', 'button-generation' ),
			'_icon-push'              => __( 'Icon Push', 'button-generation' ),
			'_icon-pop'               => __( 'Icon Pop', 'button-generation' ),
			'_icon-bounce'            => __( 'Icon Bounce', 'button-generation' ),
			'_icon-rotate'            => __( 'Icon Rotate', 'button-generation' ),
			'_icon-grow-rotate'       => __( 'Icon Grow Rotate', 'button-generation' ),
			'_icon-float'             => __( 'Icon Float', 'button-generation' ),
			'_icon-sink'              => __( 'Icon Sink', 'button-generation' ),
			'_icon-bob'               => __( 'Icon Bob', 'button-generation' ),
			'_icon-hang'              => __( 'Icon Hang', 'button-generation' ),
			'_icon-wobble-horizontal' => __( 'Icon Wobble Horizontal', 'button-generation' ),
			'_icon-wobble-vertical'   => __( 'Icon Wobble Vertical', 'button-generation' ),
			'_icon-buzz'              => __( 'Icon Buzz', 'button-generation' ),
			'_icon-buzz-out'          => __( 'Icon Buzz Out', 'button-generation' ),
		];
	}

	public static function border_effect(): array {
		return [
			'none'                          => __( 'None', 'button-generation' ),
			'_border-trim'                  => __( 'Trim', 'button-generation' ),
			'_border-ripple-out'            => __( 'Ripple Out', 'button-generation' ),
			'_border-ripple-in'             => __( 'Ripple In', 'button-generation' ),
			'_border-outline-out'           => __( 'Outline Out', 'button-generation' ),
			'_border-outline-in'            => __( 'Outline In', 'button-generation' ),
			'_border-round-corners'         => __( 'Round Corners', 'button-generation' ),
			'_border-underline-from-left'   => __( 'Underline From Left', 'button-generation' ),
			'_border-underline-from-center' => __( 'Underline From Center', 'button-generation' ),
			'_border-underline-from-right'  => __( 'Underline From Right', 'button-generation' ),
			'_border-overline-from-left'    => __( 'Overline From Left', 'button-generation' ),
			'_border-overline-from-center'  => __( 'Overline From Center', 'button-generation' ),
			'_border-overline-from-right'   => __( 'Overline From Right', 'button-generation' ),
			'_border-reveal'                => __( 'Reveal', 'button-generation' ),
			'_border-overline-reveal'       => __( 'Overline Reveal', 'button-generation' ),
			'_border-underline-reveal'      => __( 'Underline Reveal', 'button-generation' ),
		];
	}


	public static function icon_anim(): array {
		return [
			''             => __( 'None', 'button-generation' ),
			'fa-beat'      => __( 'Beat', 'button-generation' ),
			'fa-fade'      => __( 'Fade', 'button-generation' ),
			'fa-beat-fade' => __( 'Beat-Fade', 'button-generation' ),
			'fa-bounce'    => __( 'Bounce', 'button-generation' ),
			'fa-flip'      => __( 'Flip', 'button-generation' ),
			'fa-shake'     => __( 'Shake', 'button-generation' ),
			'fa-spin'      => __( 'Spin', 'button-generation' ),
		];
	}

	public static function icons() {
		$icons = [
			'',
			'fas fa-0',
			'fas fa-1',
			'fas fa-2',
			'fas fa-3',
			'fas fa-4',
			'fas fa-5',
			'fas fa-6',
			'fas fa-7',
			'fas fa-8',
			'fas fa-9',
			'fas fa-a',
			'fas fa-address-book',
			'fas fa-address-card',
			'fas fa-align-center',
			'fas fa-align-justify',
			'fas fa-align-left',
			'fas fa-align-right',
			'fas fa-anchor-circle-check',
			'fas fa-anchor-circle-exclamation',
			'fas fa-anchor-circle-xmark',
			'fas fa-anchor-lock',
			'fas fa-anchor',
			'fas fa-angle-down',
			'fas fa-angle-left',
			'fas fa-angle-right',
			'fas fa-angle-up',
			'fas fa-angles-down',
			'fas fa-angles-left',
			'fas fa-angles-right',
			'fas fa-angles-up',
			'fas fa-ankh',
			'fas fa-apple-whole',
			'fas fa-archway',
			'fas fa-arrow-down-1-9',
			'fas fa-arrow-down-9-1',
			'fas fa-arrow-down-a-z',
			'fas fa-arrow-down-long',
			'fas fa-arrow-down-short-wide',
			'fas fa-arrow-down-up-across-line',
			'fas fa-arrow-down-up-lock',
			'fas fa-arrow-down-wide-short',
			'fas fa-arrow-down-z-a',
			'fas fa-arrow-down',
			'fas fa-arrow-left-long',
			'fas fa-arrow-left',
			'fas fa-arrow-pointer',
			'fas fa-arrow-right-arrow-left',
			'fas fa-arrow-right-from-bracket',
			'fas fa-arrow-right-long',
			'fas fa-arrow-right-to-bracket',
			'fas fa-arrow-right-to-city',
			'fas fa-arrow-right',
			'fas fa-arrow-rotate-left',
			'fas fa-arrow-rotate-right',
			'fas fa-arrow-trend-down',
			'fas fa-arrow-trend-up',
			'fas fa-arrow-turn-down',
			'fas fa-arrow-turn-up',
			'fas fa-arrow-up-1-9',
			'fas fa-arrow-up-9-1',
			'fas fa-arrow-up-a-z',
			'fas fa-arrow-up-from-bracket',
			'fas fa-arrow-up-from-ground-water',
			'fas fa-arrow-up-from-water-pump',
			'fas fa-arrow-up-long',
			'fas fa-arrow-up-right-dots',
			'fas fa-arrow-up-right-from-square',
			'fas fa-arrow-up-short-wide',
			'fas fa-arrow-up-wide-short',
			'fas fa-arrow-up-z-a',
			'fas fa-arrow-up',
			'fas fa-arrows-down-to-line',
			'fas fa-arrows-down-to-people',
			'fas fa-arrows-left-right-to-line',
			'fas fa-arrows-left-right',
			'fas fa-arrows-rotate',
			'fas fa-arrows-spin',
			'fas fa-arrows-split-up-and-left',
			'fas fa-arrows-to-circle',
			'fas fa-arrows-to-dot',
			'fas fa-arrows-to-eye',
			'fas fa-arrows-turn-right',
			'fas fa-arrows-turn-to-dots',
			'fas fa-arrows-up-down-left-right',
			'fas fa-arrows-up-down',
			'fas fa-arrows-up-to-line',
			'fas fa-asterisk',
			'fas fa-at',
			'fas fa-atom',
			'fas fa-audio-description',
			'fas fa-austral-sign',
			'fas fa-award',
			'fas fa-b',
			'fas fa-baby-carriage',
			'fas fa-baby',
			'fas fa-backward-fast',
			'fas fa-backward-step',
			'fas fa-backward',
			'fas fa-bacon',
			'fas fa-bacteria',
			'fas fa-bacterium',
			'fas fa-bag-shopping',
			'fas fa-bahai',
			'fas fa-baht-sign',
			'fas fa-ban-smoking',
			'fas fa-ban',
			'fas fa-bandage',
			'fas fa-bangladeshi-taka-sign',
			'fas fa-barcode',
			'fas fa-bars-progress',
			'fas fa-bars-staggered',
			'fas fa-bars',
			'fas fa-baseball-bat-ball',
			'fas fa-baseball',
			'fas fa-basket-shopping',
			'fas fa-basketball',
			'fas fa-bath',
			'fas fa-battery-empty',
			'fas fa-battery-full',
			'fas fa-battery-half',
			'fas fa-battery-quarter',
			'fas fa-battery-three-quarters',
			'fas fa-bed-pulse',
			'fas fa-bed',
			'fas fa-beer-mug-empty',
			'fas fa-bell-concierge',
			'fas fa-bell-slash',
			'fas fa-bell',
			'fas fa-bezier-curve',
			'fas fa-bicycle',
			'fas fa-binoculars',
			'fas fa-biohazard',
			'fas fa-bitcoin-sign',
			'fas fa-blender-phone',
			'fas fa-blender',
			'fas fa-blog',
			'fas fa-bold',
			'fas fa-bolt-lightning',
			'fas fa-bolt',
			'fas fa-bomb',
			'fas fa-bone',
			'fas fa-bong',
			'fas fa-book-atlas',
			'fas fa-book-bible',
			'fas fa-book-bookmark',
			'fas fa-book-journal-whills',
			'fas fa-book-medical',
			'fas fa-book-open-reader',
			'fas fa-book-open',
			'fas fa-book-quran',
			'fas fa-book-skull',
			'fas fa-book-tanakh',
			'fas fa-book',
			'fas fa-bookmark',
			'fas fa-border-all',
			'fas fa-border-none',
			'fas fa-border-top-left',
			'fas fa-bore-hole',
			'fas fa-bottle-droplet',
			'fas fa-bottle-water',
			'fas fa-bowl-food',
			'fas fa-bowl-rice',
			'fas fa-bowling-ball',
			'fas fa-box-archive',
			'fas fa-box-open',
			'fas fa-box-tissue',
			'fas fa-box',
			'fas fa-boxes-packing',
			'fas fa-boxes-stacked',
			'fas fa-braille',
			'fas fa-brain',
			'fas fa-brazilian-real-sign',
			'fas fa-bread-slice',
			'fas fa-bridge-circle-check',
			'fas fa-bridge-circle-exclamation',
			'fas fa-bridge-circle-xmark',
			'fas fa-bridge-lock',
			'fas fa-bridge-water',
			'fas fa-bridge',
			'fas fa-briefcase-medical',
			'fas fa-briefcase',
			'fas fa-broom-ball',
			'fas fa-broom',
			'fas fa-brush',
			'fas fa-bucket',
			'fas fa-bug-slash',
			'fas fa-bug',
			'fas fa-bugs',
			'fas fa-building-circle-arrow-right',
			'fas fa-building-circle-check',
			'fas fa-building-circle-exclamation',
			'fas fa-building-circle-xmark',
			'fas fa-building-columns',
			'fas fa-building-flag',
			'fas fa-building-lock',
			'fas fa-building-ngo',
			'fas fa-building-shield',
			'fas fa-building-un',
			'fas fa-building-user',
			'fas fa-building-wheat',
			'fas fa-building',
			'fas fa-bullhorn',
			'fas fa-bullseye',
			'fas fa-burger',
			'fas fa-burst',
			'fas fa-bus-simple',
			'fas fa-bus',
			'fas fa-business-time',
			'fas fa-c',
			'fas fa-cable-car',
			'fas fa-cake-candles',
			'fas fa-calculator',
			'fas fa-calendar-check',
			'fas fa-calendar-day',
			'fas fa-calendar-days',
			'fas fa-calendar-minus',
			'fas fa-calendar-plus',
			'fas fa-calendar-week',
			'fas fa-calendar-xmark',
			'fas fa-calendar',
			'fas fa-camera-retro',
			'fas fa-camera-rotate',
			'fas fa-camera',
			'fas fa-campground',
			'fas fa-candy-cane',
			'fas fa-cannabis',
			'fas fa-capsules',
			'fas fa-car-battery',
			'fas fa-car-burst',
			'fas fa-car-on',
			'fas fa-car-rear',
			'fas fa-car-side',
			'fas fa-car-tunnel',
			'fas fa-car',
			'fas fa-caravan',
			'fas fa-caret-down',
			'fas fa-caret-left',
			'fas fa-caret-right',
			'fas fa-caret-up',
			'fas fa-carrot',
			'fas fa-cart-arrow-down',
			'fas fa-cart-flatbed-suitcase',
			'fas fa-cart-flatbed',
			'fas fa-cart-plus',
			'fas fa-cart-shopping',
			'fas fa-cash-register',
			'fas fa-cat',
			'fas fa-cedi-sign',
			'fas fa-cent-sign',
			'fas fa-certificate',
			'fas fa-chair',
			'fas fa-chalkboard-user',
			'fas fa-chalkboard',
			'fas fa-champagne-glasses',
			'fas fa-charging-station',
			'fas fa-chart-area',
			'fas fa-chart-bar',
			'fas fa-chart-column',
			'fas fa-chart-gantt',
			'fas fa-chart-line',
			'fas fa-chart-pie',
			'fas fa-chart-simple',
			'fas fa-check-double',
			'fas fa-check-to-slot',
			'fas fa-check',
			'fas fa-cheese',
			'fas fa-chess-bishop',
			'fas fa-chess-board',
			'fas fa-chess-king',
			'fas fa-chess-knight',
			'fas fa-chess-pawn',
			'fas fa-chess-queen',
			'fas fa-chess-rook',
			'fas fa-chess',
			'fas fa-chevron-down',
			'fas fa-chevron-left',
			'fas fa-chevron-right',
			'fas fa-chevron-up',
			'fas fa-child-combatant',
			'fas fa-child-dress',
			'fas fa-child-reaching',
			'fas fa-child',
			'fas fa-children',
			'fas fa-church',
			'fas fa-circle-arrow-down',
			'fas fa-circle-arrow-left',
			'fas fa-circle-arrow-right',
			'fas fa-circle-arrow-up',
			'fas fa-circle-check',
			'fas fa-circle-chevron-down',
			'fas fa-circle-chevron-left',
			'fas fa-circle-chevron-right',
			'fas fa-circle-chevron-up',
			'fas fa-circle-dollar-to-slot',
			'fas fa-circle-dot',
			'fas fa-circle-down',
			'fas fa-circle-exclamation',
			'fas fa-circle-h',
			'fas fa-circle-half-stroke',
			'fas fa-circle-info',
			'fas fa-circle-left',
			'fas fa-circle-minus',
			'fas fa-circle-nodes',
			'fas fa-circle-notch',
			'fas fa-circle-pause',
			'fas fa-circle-play',
			'fas fa-circle-plus',
			'fas fa-circle-question',
			'fas fa-circle-radiation',
			'fas fa-circle-right',
			'fas fa-circle-stop',
			'fas fa-circle-up',
			'fas fa-circle-user',
			'fas fa-circle-xmark',
			'fas fa-circle',
			'fas fa-city',
			'fas fa-clapperboard',
			'fas fa-clipboard-check',
			'fas fa-clipboard-list',
			'fas fa-clipboard-question',
			'fas fa-clipboard-user',
			'fas fa-clipboard',
			'fas fa-clock-rotate-left',
			'fas fa-clock',
			'fas fa-clone',
			'fas fa-closed-captioning',
			'fas fa-cloud-arrow-down',
			'fas fa-cloud-arrow-up',
			'fas fa-cloud-bolt',
			'fas fa-cloud-meatball',
			'fas fa-cloud-moon-rain',
			'fas fa-cloud-moon',
			'fas fa-cloud-rain',
			'fas fa-cloud-showers-heavy',
			'fas fa-cloud-showers-water',
			'fas fa-cloud-sun-rain',
			'fas fa-cloud-sun',
			'fas fa-cloud',
			'fas fa-clover',
			'fas fa-code-branch',
			'fas fa-code-commit',
			'fas fa-code-compare',
			'fas fa-code-fork',
			'fas fa-code-merge',
			'fas fa-code-pull-request',
			'fas fa-code',
			'fas fa-coins',
			'fas fa-colon-sign',
			'fas fa-comment-dollar',
			'fas fa-comment-dots',
			'fas fa-comment-medical',
			'fas fa-comment-slash',
			'fas fa-comment-sms',
			'fas fa-comment',
			'fas fa-comments-dollar',
			'fas fa-comments',
			'fas fa-compact-disc',
			'fas fa-compass-drafting',
			'fas fa-compass',
			'fas fa-compress',
			'fas fa-computer-mouse',
			'fas fa-computer',
			'fas fa-cookie-bite',
			'fas fa-cookie',
			'fas fa-copy',
			'fas fa-copyright',
			'fas fa-couch',
			'fas fa-cow',
			'fas fa-credit-card',
			'fas fa-crop-simple',
			'fas fa-crop',
			'fas fa-cross',
			'fas fa-crosshairs',
			'fas fa-crow',
			'fas fa-crown',
			'fas fa-crutch',
			'fas fa-cruzeiro-sign',
			'fas fa-cube',
			'fas fa-cubes-stacked',
			'fas fa-cubes',
			'fas fa-d',
			'fas fa-database',
			'fas fa-delete-left',
			'fas fa-democrat',
			'fas fa-desktop',
			'fas fa-dharmachakra',
			'fas fa-diagram-next',
			'fas fa-diagram-predecessor',
			'fas fa-diagram-project',
			'fas fa-diagram-successor',
			'fas fa-diamond-turn-right',
			'fas fa-diamond',
			'fas fa-dice-d6',
			'fas fa-dice-d20',
			'fas fa-dice-five',
			'fas fa-dice-four',
			'fas fa-dice-one',
			'fas fa-dice-six',
			'fas fa-dice-three',
			'fas fa-dice-two',
			'fas fa-dice',
			'fas fa-disease',
			'fas fa-display',
			'fas fa-divide',
			'fas fa-dna',
			'fas fa-dog',
			'fas fa-dollar-sign',
			'fas fa-dolly',
			'fas fa-dong-sign',
			'fas fa-door-closed',
			'fas fa-door-open',
			'fas fa-dove',
			'fas fa-down-left-and-up-right-to-center',
			'fas fa-down-long',
			'fas fa-download',
			'fas fa-dragon',
			'fas fa-draw-polygon',
			'fas fa-droplet-slash',
			'fas fa-droplet',
			'fas fa-drum-steelpan',
			'fas fa-drum',
			'fas fa-drumstick-bite',
			'fas fa-dumbbell',
			'fas fa-dumpster-fire',
			'fas fa-dumpster',
			'fas fa-dungeon',
			'fas fa-e',
			'fas fa-ear-deaf',
			'fas fa-ear-listen',
			'fas fa-earth-africa',
			'fas fa-earth-americas',
			'fas fa-earth-asia',
			'fas fa-earth-europe',
			'fas fa-earth-oceania',
			'fas fa-egg',
			'fas fa-eject',
			'fas fa-elevator',
			'fas fa-ellipsis-vertical',
			'fas fa-ellipsis',
			'fas fa-envelope-circle-check',
			'fas fa-envelope-open-text',
			'fas fa-envelope-open',
			'fas fa-envelope',
			'fas fa-envelopes-bulk',
			'fas fa-equals',
			'fas fa-eraser',
			'fas fa-ethernet',
			'fas fa-euro-sign',
			'fas fa-exclamation',
			'fas fa-expand',
			'fas fa-explosion',
			'fas fa-eye-dropper',
			'fas fa-eye-low-vision',
			'fas fa-eye-slash',
			'fas fa-eye',
			'fas fa-f',
			'fas fa-face-angry',
			'fas fa-face-dizzy',
			'fas fa-face-flushed',
			'fas fa-face-frown-open',
			'fas fa-face-frown',
			'fas fa-face-grimace',
			'fas fa-face-grin-beam-sweat',
			'fas fa-face-grin-beam',
			'fas fa-face-grin-hearts',
			'fas fa-face-grin-squint-tears',
			'fas fa-face-grin-squint',
			'fas fa-face-grin-stars',
			'fas fa-face-grin-tears',
			'fas fa-face-grin-tongue-squint',
			'fas fa-face-grin-tongue-wink',
			'fas fa-face-grin-tongue',
			'fas fa-face-grin-wide',
			'fas fa-face-grin-wink',
			'fas fa-face-grin',
			'fas fa-face-kiss-beam',
			'fas fa-face-kiss-wink-heart',
			'fas fa-face-kiss',
			'fas fa-face-laugh-beam',
			'fas fa-face-laugh-squint',
			'fas fa-face-laugh-wink',
			'fas fa-face-laugh',
			'fas fa-face-meh-blank',
			'fas fa-face-meh',
			'fas fa-face-rolling-eyes',
			'fas fa-face-sad-cry',
			'fas fa-face-sad-tear',
			'fas fa-face-smile-beam',
			'fas fa-face-smile-wink',
			'fas fa-face-smile',
			'fas fa-face-surprise',
			'fas fa-face-tired',
			'fas fa-fan',
			'fas fa-faucet-drip',
			'fas fa-faucet',
			'fas fa-fax',
			'fas fa-feather-pointed',
			'fas fa-feather',
			'fas fa-ferry',
			'fas fa-file-arrow-down',
			'fas fa-file-arrow-up',
			'fas fa-file-audio',
			'fas fa-file-circle-check',
			'fas fa-file-circle-exclamation',
			'fas fa-file-circle-minus',
			'fas fa-file-circle-plus',
			'fas fa-file-circle-question',
			'fas fa-file-circle-xmark',
			'fas fa-file-code',
			'fas fa-file-contract',
			'fas fa-file-csv',
			'fas fa-file-excel',
			'fas fa-file-export',
			'fas fa-file-image',
			'fas fa-file-import',
			'fas fa-file-invoice-dollar',
			'fas fa-file-invoice',
			'fas fa-file-lines',
			'fas fa-file-medical',
			'fas fa-file-pdf',
			'fas fa-file-pen',
			'fas fa-file-powerpoint',
			'fas fa-file-prescription',
			'fas fa-file-shield',
			'fas fa-file-signature',
			'fas fa-file-video',
			'fas fa-file-waveform',
			'fas fa-file-word',
			'fas fa-file-zipper',
			'fas fa-file',
			'fas fa-fill-drip',
			'fas fa-fill',
			'fas fa-film',
			'fas fa-filter-circle-dollar',
			'fas fa-filter-circle-xmark',
			'fas fa-filter',
			'fas fa-fingerprint',
			'fas fa-fire-burner',
			'fas fa-fire-extinguisher',
			'fas fa-fire-flame-curved',
			'fas fa-fire-flame-simple',
			'fas fa-fire',
			'fas fa-fish-fins',
			'fas fa-fish',
			'fas fa-flag-checkered',
			'fas fa-flag-usa',
			'fas fa-flag',
			'fas fa-flask-vial',
			'fas fa-flask',
			'fas fa-floppy-disk',
			'fas fa-florin-sign',
			'fas fa-folder-closed',
			'fas fa-folder-minus',
			'fas fa-folder-open',
			'fas fa-folder-plus',
			'fas fa-folder-tree',
			'fas fa-folder',
			'fas fa-font-awesome',
			'fas fa-font',
			'fas fa-football',
			'fas fa-forward-fast',
			'fas fa-forward-step',
			'fas fa-forward',
			'fas fa-franc-sign',
			'fas fa-frog',
			'fas fa-futbol',
			'fas fa-g',
			'fas fa-gamepad',
			'fas fa-gas-pump',
			'fas fa-gauge-high',
			'fas fa-gauge-simple-high',
			'fas fa-gauge-simple',
			'fas fa-gauge',
			'fas fa-gavel',
			'fas fa-gear',
			'fas fa-gears',
			'fas fa-gem',
			'fas fa-genderless',
			'fas fa-ghost',
			'fas fa-gift',
			'fas fa-gifts',
			'fas fa-glass-water-droplet',
			'fas fa-glass-water',
			'fas fa-glasses',
			'fas fa-globe',
			'fas fa-golf-ball-tee',
			'fas fa-gopuram',
			'fas fa-graduation-cap',
			'fas fa-greater-than-equal',
			'fas fa-greater-than',
			'fas fa-grip-lines-vertical',
			'fas fa-grip-lines',
			'fas fa-grip-vertical',
			'fas fa-grip',
			'fas fa-group-arrows-rotate',
			'fas fa-guarani-sign',
			'fas fa-guitar',
			'fas fa-gun',
			'fas fa-h',
			'fas fa-hammer',
			'fas fa-hamsa',
			'fas fa-hand-back-fist',
			'fas fa-hand-dots',
			'fas fa-hand-fist',
			'fas fa-hand-holding-dollar',
			'fas fa-hand-holding-droplet',
			'fas fa-hand-holding-hand',
			'fas fa-hand-holding-heart',
			'fas fa-hand-holding-medical',
			'fas fa-hand-holding',
			'fas fa-hand-lizard',
			'fas fa-hand-middle-finger',
			'fas fa-hand-peace',
			'fas fa-hand-point-down',
			'fas fa-hand-point-left',
			'fas fa-hand-point-right',
			'fas fa-hand-point-up',
			'fas fa-hand-pointer',
			'fas fa-hand-scissors',
			'fas fa-hand-sparkles',
			'fas fa-hand-spock',
			'fas fa-hand',
			'fas fa-handcuffs',
			'fas fa-hands-asl-interpreting',
			'fas fa-hands-bound',
			'fas fa-hands-bubbles',
			'fas fa-hands-clapping',
			'fas fa-hands-holding-child',
			'fas fa-hands-holding-circle',
			'fas fa-hands-holding',
			'fas fa-hands-praying',
			'fas fa-hands',
			'fas fa-handshake-angle',
			'fas fa-handshake-simple-slash',
			'fas fa-handshake-simple',
			'fas fa-handshake-slash',
			'fas fa-handshake',
			'fas fa-hanukiah',
			'fas fa-hard-drive',
			'fas fa-hashtag',
			'fas fa-hat-cowboy-side',
			'fas fa-hat-cowboy',
			'fas fa-hat-wizard',
			'fas fa-head-side-cough-slash',
			'fas fa-head-side-cough',
			'fas fa-head-side-mask',
			'fas fa-head-side-virus',
			'fas fa-heading',
			'fas fa-headphones-simple',
			'fas fa-headphones',
			'fas fa-headset',
			'fas fa-heart-circle-bolt',
			'fas fa-heart-circle-check',
			'fas fa-heart-circle-exclamation',
			'fas fa-heart-circle-minus',
			'fas fa-heart-circle-plus',
			'fas fa-heart-circle-xmark',
			'fas fa-heart-crack',
			'fas fa-heart-pulse',
			'fas fa-heart',
			'fas fa-helicopter-symbol',
			'fas fa-helicopter',
			'fas fa-helmet-safety',
			'fas fa-helmet-un',
			'fas fa-highlighter',
			'fas fa-hill-avalanche',
			'fas fa-hill-rockslide',
			'fas fa-hippo',
			'fas fa-hockey-puck',
			'fas fa-holly-berry',
			'fas fa-horse-head',
			'fas fa-horse',
			'fas fa-hospital-user',
			'fas fa-hospital',
			'fas fa-hot-tub-person',
			'fas fa-hotdog',
			'fas fa-hotel',
			'fas fa-hourglass-end',
			'fas fa-hourglass-half',
			'fas fa-hourglass-start',
			'fas fa-hourglass',
			'fas fa-house-chimney-crack',
			'fas fa-house-chimney-medical',
			'fas fa-house-chimney-user',
			'fas fa-house-chimney-window',
			'fas fa-house-chimney',
			'fas fa-house-circle-check',
			'fas fa-house-circle-exclamation',
			'fas fa-house-circle-xmark',
			'fas fa-house-crack',
			'fas fa-house-fire',
			'fas fa-house-flag',
			'fas fa-house-flood-water-circle-arrow-right',
			'fas fa-house-flood-water',
			'fas fa-house-laptop',
			'fas fa-house-lock',
			'fas fa-house-medical-circle-check',
			'fas fa-house-medical-circle-exclamation',
			'fas fa-house-medical-circle-xmark',
			'fas fa-house-medical-flag',
			'fas fa-house-medical',
			'fas fa-house-signal',
			'fas fa-house-tsunami',
			'fas fa-house-user',
			'fas fa-house',
			'fas fa-hryvnia-sign',
			'fas fa-hurricane',
			'fas fa-i-cursor',
			'fas fa-i',
			'fas fa-ice-cream',
			'fas fa-icicles',
			'fas fa-icons',
			'fas fa-id-badge',
			'fas fa-id-card-clip',
			'fas fa-id-card',
			'fas fa-igloo',
			'fas fa-image-portrait',
			'fas fa-image',
			'fas fa-images',
			'fas fa-inbox',
			'fas fa-indent',
			'fas fa-indian-rupee-sign',
			'fas fa-industry',
			'fas fa-infinity',
			'fas fa-info',
			'fas fa-italic',
			'fas fa-j',
			'fas fa-jar-wheat',
			'fas fa-jar',
			'fas fa-jedi',
			'fas fa-jet-fighter-up',
			'fas fa-jet-fighter',
			'fas fa-joint',
			'fas fa-jug-detergent',
			'fas fa-k',
			'fas fa-kaaba',
			'fas fa-key',
			'fas fa-keyboard',
			'fas fa-khanda',
			'fas fa-kip-sign',
			'fas fa-kit-medical',
			'fas fa-kitchen-set',
			'fas fa-kiwi-bird',
			'fas fa-l',
			'fas fa-land-mine-on',
			'fas fa-landmark-dome',
			'fas fa-landmark-flag',
			'fas fa-landmark',
			'fas fa-language',
			'fas fa-laptop-code',
			'fas fa-laptop-file',
			'fas fa-laptop-medical',
			'fas fa-laptop',
			'fas fa-lari-sign',
			'fas fa-layer-group',
			'fas fa-leaf',
			'fas fa-left-long',
			'fas fa-left-right',
			'fas fa-lemon',
			'fas fa-less-than-equal',
			'fas fa-less-than',
			'fas fa-life-ring',
			'fas fa-lightbulb',
			'fas fa-lines-leaning',
			'fas fa-link-slash',
			'fas fa-link',
			'fas fa-lira-sign',
			'fas fa-list-check',
			'fas fa-list-ol',
			'fas fa-list-ul',
			'fas fa-list',
			'fas fa-litecoin-sign',
			'fas fa-location-arrow',
			'fas fa-location-crosshairs',
			'fas fa-location-dot',
			'fas fa-location-pin-lock',
			'fas fa-location-pin',
			'fas fa-lock-open',
			'fas fa-lock',
			'fas fa-locust',
			'fas fa-lungs-virus',
			'fas fa-lungs',
			'fas fa-m',
			'fas fa-magnet',
			'fas fa-magnifying-glass-arrow-right',
			'fas fa-magnifying-glass-chart',
			'fas fa-magnifying-glass-dollar',
			'fas fa-magnifying-glass-location',
			'fas fa-magnifying-glass-minus',
			'fas fa-magnifying-glass-plus',
			'fas fa-magnifying-glass',
			'fas fa-manat-sign',
			'fas fa-map-location-dot',
			'fas fa-map-location',
			'fas fa-map-pin',
			'fas fa-map',
			'fas fa-marker',
			'fas fa-mars-and-venus-burst',
			'fas fa-mars-and-venus',
			'fas fa-mars-double',
			'fas fa-mars-stroke-right',
			'fas fa-mars-stroke-up',
			'fas fa-mars-stroke',
			'fas fa-mars',
			'fas fa-martini-glass-citrus',
			'fas fa-martini-glass-empty',
			'fas fa-martini-glass',
			'fas fa-mask-face',
			'fas fa-mask-ventilator',
			'fas fa-mask',
			'fas fa-masks-theater',
			'fas fa-mattress-pillow',
			'fas fa-maximize',
			'fas fa-medal',
			'fas fa-memory',
			'fas fa-menorah',
			'fas fa-mercury',
			'fas fa-message',
			'fas fa-meteor',
			'fas fa-microchip',
			'fas fa-microphone-lines-slash',
			'fas fa-microphone-lines',
			'fas fa-microphone-slash',
			'fas fa-microphone',
			'fas fa-microscope',
			'fas fa-mill-sign',
			'fas fa-minimize',
			'fas fa-minus',
			'fas fa-mitten',
			'fas fa-mobile-button',
			'fas fa-mobile-retro',
			'fas fa-mobile-screen-button',
			'fas fa-mobile-screen',
			'fas fa-mobile',
			'fas fa-money-bill-1-wave',
			'fas fa-money-bill-1',
			'fas fa-money-bill-transfer',
			'fas fa-money-bill-trend-up',
			'fas fa-money-bill-wave',
			'fas fa-money-bill-wheat',
			'fas fa-money-bill',
			'fas fa-money-bills',
			'fas fa-money-check-dollar',
			'fas fa-money-check',
			'fas fa-monument',
			'fas fa-moon',
			'fas fa-mortar-pestle',
			'fas fa-mosque',
			'fas fa-mosquito-net',
			'fas fa-mosquito',
			'fas fa-motorcycle',
			'fas fa-mound',
			'fas fa-mountain-city',
			'fas fa-mountain-sun',
			'fas fa-mountain',
			'fas fa-mug-hot',
			'fas fa-mug-saucer',
			'fas fa-music',
			'fas fa-n',
			'fas fa-naira-sign',
			'fas fa-network-wired',
			'fas fa-neuter',
			'fas fa-newspaper',
			'fas fa-not-equal',
			'fas fa-notdef',
			'fas fa-note-sticky',
			'fas fa-notes-medical',
			'fas fa-o',
			'fas fa-object-group',
			'fas fa-object-ungroup',
			'fas fa-oil-can',
			'fas fa-oil-well',
			'fas fa-om',
			'fas fa-otter',
			'fas fa-outdent',
			'fas fa-p',
			'fas fa-pager',
			'fas fa-paint-roller',
			'fas fa-paintbrush',
			'fas fa-palette',
			'fas fa-pallet',
			'fas fa-panorama',
			'fas fa-paper-plane',
			'fas fa-paperclip',
			'fas fa-parachute-box',
			'fas fa-paragraph',
			'fas fa-passport',
			'fas fa-paste',
			'fas fa-pause',
			'fas fa-paw',
			'fas fa-peace',
			'fas fa-pen-clip',
			'fas fa-pen-fancy',
			'fas fa-pen-nib',
			'fas fa-pen-ruler',
			'fas fa-pen-to-square',
			'fas fa-pen',
			'fas fa-pencil',
			'fas fa-people-arrows',
			'fas fa-people-carry-box',
			'fas fa-people-group',
			'fas fa-people-line',
			'fas fa-people-pulling',
			'fas fa-people-robbery',
			'fas fa-people-roof',
			'fas fa-pepper-hot',
			'fas fa-percent',
			'fas fa-person-arrow-down-to-line',
			'fas fa-person-arrow-up-from-line',
			'fas fa-person-biking',
			'fas fa-person-booth',
			'fas fa-person-breastfeeding',
			'fas fa-person-burst',
			'fas fa-person-cane',
			'fas fa-person-chalkboard',
			'fas fa-person-circle-check',
			'fas fa-person-circle-exclamation',
			'fas fa-person-circle-minus',
			'fas fa-person-circle-plus',
			'fas fa-person-circle-question',
			'fas fa-person-circle-xmark',
			'fas fa-person-digging',
			'fas fa-person-dots-from-line',
			'fas fa-person-dress-burst',
			'fas fa-person-dress',
			'fas fa-person-drowning',
			'fas fa-person-falling-burst',
			'fas fa-person-falling',
			'fas fa-person-half-dress',
			'fas fa-person-harassing',
			'fas fa-person-hiking',
			'fas fa-person-military-pointing',
			'fas fa-person-military-rifle',
			'fas fa-person-military-to-person',
			'fas fa-person-praying',
			'fas fa-person-pregnant',
			'fas fa-person-rays',
			'fas fa-person-rifle',
			'fas fa-person-running',
			'fas fa-person-shelter',
			'fas fa-person-skating',
			'fas fa-person-skiing-nordic',
			'fas fa-person-skiing',
			'fas fa-person-snowboarding',
			'fas fa-person-swimming',
			'fas fa-person-through-window',
			'fas fa-person-walking-arrow-loop-left',
			'fas fa-person-walking-arrow-right',
			'fas fa-person-walking-dashed-line-arrow-right',
			'fas fa-person-walking-luggage',
			'fas fa-person-walking-with-cane',
			'fas fa-person-walking',
			'fas fa-person',
			'fas fa-peseta-sign',
			'fas fa-peso-sign',
			'fas fa-phone-flip',
			'fas fa-phone-slash',
			'fas fa-phone-volume',
			'fas fa-phone',
			'fas fa-photo-film',
			'fas fa-piggy-bank',
			'fas fa-pills',
			'fas fa-pizza-slice',
			'fas fa-place-of-worship',
			'fas fa-plane-arrival',
			'fas fa-plane-circle-check',
			'fas fa-plane-circle-exclamation',
			'fas fa-plane-circle-xmark',
			'fas fa-plane-departure',
			'fas fa-plane-lock',
			'fas fa-plane-slash',
			'fas fa-plane-up',
			'fas fa-plane',
			'fas fa-plant-wilt',
			'fas fa-plate-wheat',
			'fas fa-play',
			'fas fa-plug-circle-bolt',
			'fas fa-plug-circle-check',
			'fas fa-plug-circle-exclamation',
			'fas fa-plug-circle-minus',
			'fas fa-plug-circle-plus',
			'fas fa-plug-circle-xmark',
			'fas fa-plug',
			'fas fa-plus-minus',
			'fas fa-plus',
			'fas fa-podcast',
			'fas fa-poo-storm',
			'fas fa-poo',
			'fas fa-poop',
			'fas fa-power-off',
			'fas fa-prescription-bottle-medical',
			'fas fa-prescription-bottle',
			'fas fa-prescription',
			'fas fa-print',
			'fas fa-pump-medical',
			'fas fa-pump-soap',
			'fas fa-puzzle-piece',
			'fas fa-q',
			'fas fa-qrcode',
			'fas fa-question',
			'fas fa-quote-left',
			'fas fa-quote-right',
			'fas fa-r',
			'fas fa-radiation',
			'fas fa-radio',
			'fas fa-rainbow',
			'fas fa-ranking-star',
			'fas fa-receipt',
			'fas fa-record-vinyl',
			'fas fa-rectangle-ad',
			'fas fa-rectangle-list',
			'fas fa-rectangle-xmark',
			'fas fa-recycle',
			'fas fa-registered',
			'fas fa-repeat',
			'fas fa-reply-all',
			'fas fa-reply',
			'fas fa-republican',
			'fas fa-restroom',
			'fas fa-retweet',
			'fas fa-ribbon',
			'fas fa-right-from-bracket',
			'fas fa-right-left',
			'fas fa-right-long',
			'fas fa-right-to-bracket',
			'fas fa-ring',
			'fas fa-road-barrier',
			'fas fa-road-bridge',
			'fas fa-road-circle-check',
			'fas fa-road-circle-exclamation',
			'fas fa-road-circle-xmark',
			'fas fa-road-lock',
			'fas fa-road-spikes',
			'fas fa-road',
			'fas fa-robot',
			'fas fa-rocket',
			'fas fa-rotate-left',
			'fas fa-rotate-right',
			'fas fa-rotate',
			'fas fa-route',
			'fas fa-rss',
			'fas fa-ruble-sign',
			'fas fa-rug',
			'fas fa-ruler-combined',
			'fas fa-ruler-horizontal',
			'fas fa-ruler-vertical',
			'fas fa-ruler',
			'fas fa-rupee-sign',
			'fas fa-rupiah-sign',
			'fas fa-s',
			'fas fa-sack-dollar',
			'fas fa-sack-xmark',
			'fas fa-sailboat',
			'fas fa-satellite-dish',
			'fas fa-satellite',
			'fas fa-scale-balanced',
			'fas fa-scale-unbalanced-flip',
			'fas fa-scale-unbalanced',
			'fas fa-school-circle-check',
			'fas fa-school-circle-exclamation',
			'fas fa-school-circle-xmark',
			'fas fa-school-flag',
			'fas fa-school-lock',
			'fas fa-school',
			'fas fa-scissors',
			'fas fa-screwdriver-wrench',
			'fas fa-screwdriver',
			'fas fa-scroll-torah',
			'fas fa-scroll',
			'fas fa-sd-card',
			'fas fa-section',
			'fas fa-seedling',
			'fas fa-server',
			'fas fa-shapes',
			'fas fa-share-from-square',
			'fas fa-share-nodes',
			'fas fa-share',
			'fas fa-sheet-plastic',
			'fas fa-shekel-sign',
			'fas fa-shield-cat',
			'fas fa-shield-dog',
			'fas fa-shield-halved',
			'fas fa-shield-heart',
			'fas fa-shield-virus',
			'fas fa-shield',
			'fas fa-ship',
			'fas fa-shirt',
			'fas fa-shoe-prints',
			'fas fa-shop-lock',
			'fas fa-shop-slash',
			'fas fa-shop',
			'fas fa-shower',
			'fas fa-shrimp',
			'fas fa-shuffle',
			'fas fa-shuttle-space',
			'fas fa-sign-hanging',
			'fas fa-signal',
			'fas fa-signature',
			'fas fa-signs-post',
			'fas fa-sim-card',
			'fas fa-sink',
			'fas fa-sitemap',
			'fas fa-skull-crossbones',
			'fas fa-skull',
			'fas fa-slash',
			'fas fa-sleigh',
			'fas fa-sliders',
			'fas fa-smog',
			'fas fa-smoking',
			'fas fa-snowflake',
			'fas fa-snowman',
			'fas fa-snowplow',
			'fas fa-soap',
			'fas fa-socks',
			'fas fa-solar-panel',
			'fas fa-sort-down',
			'fas fa-sort-up',
			'fas fa-sort',
			'fas fa-spa',
			'fas fa-spaghetti-monster-flying',
			'fas fa-spell-check',
			'fas fa-spider',
			'fas fa-spinner',
			'fas fa-splotch',
			'fas fa-spoon',
			'fas fa-spray-can-sparkles',
			'fas fa-spray-can',
			'fas fa-square-arrow-up-right',
			'fas fa-square-caret-down',
			'fas fa-square-caret-left',
			'fas fa-square-caret-right',
			'fas fa-square-caret-up',
			'fas fa-square-check',
			'fas fa-square-envelope',
			'fas fa-square-full',
			'fas fa-square-h',
			'fas fa-square-minus',
			'fas fa-square-nfi',
			'fas fa-square-parking',
			'fas fa-square-pen',
			'fas fa-square-person-confined',
			'fas fa-square-phone-flip',
			'fas fa-square-phone',
			'fas fa-square-plus',
			'fas fa-square-poll-horizontal',
			'fas fa-square-poll-vertical',
			'fas fa-square-root-variable',
			'fas fa-square-rss',
			'fas fa-square-share-nodes',
			'fas fa-square-up-right',
			'fas fa-square-virus',
			'fas fa-square-xmark',
			'fas fa-square',
			'fas fa-staff-snake',
			'fas fa-stairs',
			'fas fa-stamp',
			'fas fa-stapler',
			'fas fa-star-and-crescent',
			'fas fa-star-half-stroke',
			'fas fa-star-half',
			'fas fa-star-of-david',
			'fas fa-star-of-life',
			'fas fa-star',
			'fas fa-sterling-sign',
			'fas fa-stethoscope',
			'fas fa-stop',
			'fas fa-stopwatch-20',
			'fas fa-stopwatch',
			'fas fa-store-slash',
			'fas fa-store',
			'fas fa-street-view',
			'fas fa-strikethrough',
			'fas fa-stroopwafel',
			'fas fa-subscript',
			'fas fa-suitcase-medical',
			'fas fa-suitcase-rolling',
			'fas fa-suitcase',
			'fas fa-sun-plant-wilt',
			'fas fa-sun',
			'fas fa-superscript',
			'fas fa-swatchbook',
			'fas fa-synagogue',
			'fas fa-syringe',
			'fas fa-t',
			'fas fa-table-cells-column-lock',
			'fas fa-table-cells-large',
			'fas fa-table-cells-row-lock',
			'fas fa-table-cells-row-unlock',
			'fas fa-table-cells',
			'fas fa-table-columns',
			'fas fa-table-list',
			'fas fa-table-tennis-paddle-ball',
			'fas fa-table',
			'fas fa-tablet-button',
			'fas fa-tablet-screen-button',
			'fas fa-tablet',
			'fas fa-tablets',
			'fas fa-tachograph-digital',
			'fas fa-tag',
			'fas fa-tags',
			'fas fa-tape',
			'fas fa-tarp-droplet',
			'fas fa-tarp',
			'fas fa-taxi',
			'fas fa-teeth-open',
			'fas fa-teeth',
			'fas fa-temperature-arrow-down',
			'fas fa-temperature-arrow-up',
			'fas fa-temperature-empty',
			'fas fa-temperature-full',
			'fas fa-temperature-half',
			'fas fa-temperature-high',
			'fas fa-temperature-low',
			'fas fa-temperature-quarter',
			'fas fa-temperature-three-quarters',
			'fas fa-tenge-sign',
			'fas fa-tent-arrow-down-to-line',
			'fas fa-tent-arrow-left-right',
			'fas fa-tent-arrow-turn-left',
			'fas fa-tent-arrows-down',
			'fas fa-tent',
			'fas fa-tents',
			'fas fa-terminal',
			'fas fa-text-height',
			'fas fa-text-slash',
			'fas fa-text-width',
			'fas fa-thermometer',
			'fas fa-thumbs-down',
			'fas fa-thumbs-up',
			'fas fa-thumbtack-slash',
			'fas fa-thumbtack',
			'fas fa-ticket-simple',
			'fas fa-ticket',
			'fas fa-timeline',
			'fas fa-toggle-off',
			'fas fa-toggle-on',
			'fas fa-toilet-paper-slash',
			'fas fa-toilet-paper',
			'fas fa-toilet-portable',
			'fas fa-toilet',
			'fas fa-toilets-portable',
			'fas fa-toolbox',
			'fas fa-tooth',
			'fas fa-torii-gate',
			'fas fa-tornado',
			'fas fa-tower-broadcast',
			'fas fa-tower-cell',
			'fas fa-tower-observation',
			'fas fa-tractor',
			'fas fa-trademark',
			'fas fa-traffic-light',
			'fas fa-trailer',
			'fas fa-train-subway',
			'fas fa-train-tram',
			'fas fa-train',
			'fas fa-transgender',
			'fas fa-trash-arrow-up',
			'fas fa-trash-can-arrow-up',
			'fas fa-trash-can',
			'fas fa-trash',
			'fas fa-tree-city',
			'fas fa-tree',
			'fas fa-triangle-exclamation',
			'fas fa-trophy',
			'fas fa-trowel-bricks',
			'fas fa-trowel',
			'fas fa-truck-arrow-right',
			'fas fa-truck-droplet',
			'fas fa-truck-fast',
			'fas fa-truck-field-un',
			'fas fa-truck-field',
			'fas fa-truck-front',
			'fas fa-truck-medical',
			'fas fa-truck-monster',
			'fas fa-truck-moving',
			'fas fa-truck-pickup',
			'fas fa-truck-plane',
			'fas fa-truck-ramp-box',
			'fas fa-truck',
			'fas fa-tty',
			'fas fa-turkish-lira-sign',
			'fas fa-turn-down',
			'fas fa-turn-up',
			'fas fa-tv',
			'fas fa-u',
			'fas fa-umbrella-beach',
			'fas fa-umbrella',
			'fas fa-underline',
			'fas fa-universal-access',
			'fas fa-unlock-keyhole',
			'fas fa-unlock',
			'fas fa-up-down-left-right',
			'fas fa-up-down',
			'fas fa-up-long',
			'fas fa-up-right-and-down-left-from-center',
			'fas fa-up-right-from-square',
			'fas fa-upload',
			'fas fa-user-astronaut',
			'fas fa-user-check',
			'fas fa-user-clock',
			'fas fa-user-doctor',
			'fas fa-user-gear',
			'fas fa-user-graduate',
			'fas fa-user-group',
			'fas fa-user-injured',
			'fas fa-user-large-slash',
			'fas fa-user-large',
			'fas fa-user-lock',
			'fas fa-user-minus',
			'fas fa-user-ninja',
			'fas fa-user-nurse',
			'fas fa-user-pen',
			'fas fa-user-plus',
			'fas fa-user-secret',
			'fas fa-user-shield',
			'fas fa-user-slash',
			'fas fa-user-tag',
			'fas fa-user-tie',
			'fas fa-user-xmark',
			'fas fa-user',
			'fas fa-users-between-lines',
			'fas fa-users-gear',
			'fas fa-users-line',
			'fas fa-users-rays',
			'fas fa-users-rectangle',
			'fas fa-users-slash',
			'fas fa-users-viewfinder',
			'fas fa-users',
			'fas fa-utensils',
			'fas fa-v',
			'fas fa-van-shuttle',
			'fas fa-vault',
			'fas fa-vector-square',
			'fas fa-venus-double',
			'fas fa-venus-mars',
			'fas fa-venus',
			'fas fa-vest-patches',
			'fas fa-vest',
			'fas fa-vial-circle-check',
			'fas fa-vial-virus',
			'fas fa-vial',
			'fas fa-vials',
			'fas fa-video-slash',
			'fas fa-video',
			'fas fa-vihara',
			'fas fa-virus-covid-slash',
			'fas fa-virus-covid',
			'fas fa-virus-slash',
			'fas fa-virus',
			'fas fa-viruses',
			'fas fa-voicemail',
			'fas fa-volcano',
			'fas fa-volleyball',
			'fas fa-volume-high',
			'fas fa-volume-low',
			'fas fa-volume-off',
			'fas fa-volume-xmark',
			'fas fa-vr-cardboard',
			'fas fa-w',
			'fas fa-walkie-talkie',
			'fas fa-wallet',
			'fas fa-wand-magic-sparkles',
			'fas fa-wand-magic',
			'fas fa-wand-sparkles',
			'fas fa-warehouse',
			'fas fa-water-ladder',
			'fas fa-water',
			'fas fa-wave-square',
			'fas fa-web-awesome',
			'fas fa-weight-hanging',
			'fas fa-weight-scale',
			'fas fa-wheat-awn-circle-exclamation',
			'fas fa-wheat-awn',
			'fas fa-wheelchair-move',
			'fas fa-wheelchair',
			'fas fa-whiskey-glass',
			'fas fa-wifi',
			'fas fa-wind',
			'fas fa-window-maximize',
			'fas fa-window-minimize',
			'fas fa-window-restore',
			'fas fa-wine-bottle',
			'fas fa-wine-glass-empty',
			'fas fa-wine-glass',
			'fas fa-won-sign',
			'fas fa-worm',
			'fas fa-wrench',
			'fas fa-x-ray',
			'fas fa-x',
			'fas fa-xmark',
			'fas fa-xmarks-lines',
			'fas fa-y',
			'fas fa-yen-sign',
			'fas fa-yin-yang',
			'fas fa-z',
			'far fa-address-book',
			'far fa-address-card',
			'far fa-bell-slash',
			'far fa-bell',
			'far fa-bookmark',
			'far fa-building',
			'far fa-calendar-check',
			'far fa-calendar-days',
			'far fa-calendar-minus',
			'far fa-calendar-plus',
			'far fa-calendar-xmark',
			'far fa-calendar',
			'far fa-chart-bar',
			'far fa-chess-bishop',
			'far fa-chess-king',
			'far fa-chess-knight',
			'far fa-chess-pawn',
			'far fa-chess-queen',
			'far fa-chess-rook',
			'far fa-circle-check',
			'far fa-circle-dot',
			'far fa-circle-down',
			'far fa-circle-left',
			'far fa-circle-pause',
			'far fa-circle-play',
			'far fa-circle-question',
			'far fa-circle-right',
			'far fa-circle-stop',
			'far fa-circle-up',
			'far fa-circle-user',
			'far fa-circle-xmark',
			'far fa-circle',
			'far fa-clipboard',
			'far fa-clock',
			'far fa-clone',
			'far fa-closed-captioning',
			'far fa-comment-dots',
			'far fa-comment',
			'far fa-comments',
			'far fa-compass',
			'far fa-copy',
			'far fa-copyright',
			'far fa-credit-card',
			'far fa-envelope-open',
			'far fa-envelope',
			'far fa-eye-slash',
			'far fa-eye',
			'far fa-face-angry',
			'far fa-face-dizzy',
			'far fa-face-flushed',
			'far fa-face-frown-open',
			'far fa-face-frown',
			'far fa-face-grimace',
			'far fa-face-grin-beam-sweat',
			'far fa-face-grin-beam',
			'far fa-face-grin-hearts',
			'far fa-face-grin-squint-tears',
			'far fa-face-grin-squint',
			'far fa-face-grin-stars',
			'far fa-face-grin-tears',
			'far fa-face-grin-tongue-squint',
			'far fa-face-grin-tongue-wink',
			'far fa-face-grin-tongue',
			'far fa-face-grin-wide',
			'far fa-face-grin-wink',
			'far fa-face-grin',
			'far fa-face-kiss-beam',
			'far fa-face-kiss-wink-heart',
			'far fa-face-kiss',
			'far fa-face-laugh-beam',
			'far fa-face-laugh-squint',
			'far fa-face-laugh-wink',
			'far fa-face-laugh',
			'far fa-face-meh-blank',
			'far fa-face-meh',
			'far fa-face-rolling-eyes',
			'far fa-face-sad-cry',
			'far fa-face-sad-tear',
			'far fa-face-smile-beam',
			'far fa-face-smile-wink',
			'far fa-face-smile',
			'far fa-face-surprise',
			'far fa-face-tired',
			'far fa-file-audio',
			'far fa-file-code',
			'far fa-file-excel',
			'far fa-file-image',
			'far fa-file-lines',
			'far fa-file-pdf',
			'far fa-file-powerpoint',
			'far fa-file-video',
			'far fa-file-word',
			'far fa-file-zipper',
			'far fa-file',
			'far fa-flag',
			'far fa-floppy-disk',
			'far fa-folder-closed',
			'far fa-folder-open',
			'far fa-folder',
			'far fa-font-awesome',
			'far fa-futbol',
			'far fa-gem',
			'far fa-hand-back-fist',
			'far fa-hand-lizard',
			'far fa-hand-peace',
			'far fa-hand-point-down',
			'far fa-hand-point-left',
			'far fa-hand-point-right',
			'far fa-hand-point-up',
			'far fa-hand-pointer',
			'far fa-hand-scissors',
			'far fa-hand-spock',
			'far fa-hand',
			'far fa-handshake',
			'far fa-hard-drive',
			'far fa-heart',
			'far fa-hospital',
			'far fa-hourglass-half',
			'far fa-hourglass',
			'far fa-id-badge',
			'far fa-id-card',
			'far fa-image',
			'far fa-images',
			'far fa-keyboard',
			'far fa-lemon',
			'far fa-life-ring',
			'far fa-lightbulb',
			'far fa-map',
			'far fa-message',
			'far fa-money-bill-1',
			'far fa-moon',
			'far fa-newspaper',
			'far fa-note-sticky',
			'far fa-object-group',
			'far fa-object-ungroup',
			'far fa-paper-plane',
			'far fa-paste',
			'far fa-pen-to-square',
			'far fa-rectangle-list',
			'far fa-rectangle-xmark',
			'far fa-registered',
			'far fa-share-from-square',
			'far fa-snowflake',
			'far fa-square-caret-down',
			'far fa-square-caret-left',
			'far fa-square-caret-right',
			'far fa-square-caret-up',
			'far fa-square-check',
			'far fa-square-full',
			'far fa-square-minus',
			'far fa-square-plus',
			'far fa-square',
			'far fa-star-half-stroke',
			'far fa-star-half',
			'far fa-star',
			'far fa-sun',
			'far fa-thumbs-down',
			'far fa-thumbs-up',
			'far fa-trash-can',
			'far fa-user',
			'far fa-window-maximize',
			'far fa-window-minimize',
			'far fa-window-restore',
			'fab fa-42-group',
			'fab fa-500px',
			'fab fa-accessible-icon',
			'fab fa-accusoft',
			'fab fa-adn',
			'fab fa-adversal',
			'fab fa-affiliatetheme',
			'fab fa-airbnb',
			'fab fa-algolia',
			'fab fa-alipay',
			'fab fa-amazon-pay',
			'fab fa-amazon',
			'fab fa-amilia',
			'fab fa-android',
			'fab fa-angellist',
			'fab fa-angrycreative',
			'fab fa-angular',
			'fab fa-app-store-ios',
			'fab fa-app-store',
			'fab fa-apper',
			'fab fa-apple-pay',
			'fab fa-apple',
			'fab fa-artstation',
			'fab fa-asymmetrik',
			'fab fa-atlassian',
			'fab fa-audible',
			'fab fa-autoprefixer',
			'fab fa-avianex',
			'fab fa-aviato',
			'fab fa-aws',
			'fab fa-bandcamp',
			'fab fa-battle-net',
			'fab fa-behance',
			'fab fa-bilibili',
			'fab fa-bimobject',
			'fab fa-bitbucket',
			'fab fa-bitcoin',
			'fab fa-bity',
			'fab fa-black-tie',
			'fab fa-blackberry',
			'fab fa-blogger-b',
			'fab fa-blogger',
			'fab fa-bluesky',
			'fab fa-bluetooth-b',
			'fab fa-bluetooth',
			'fab fa-bootstrap',
			'fab fa-bots',
			'fab fa-brave-reverse',
			'fab fa-brave',
			'fab fa-btc',
			'fab fa-buffer',
			'fab fa-buromobelexperte',
			'fab fa-buy-n-large',
			'fab fa-buysellads',
			'fab fa-canadian-maple-leaf',
			'fab fa-cc-amazon-pay',
			'fab fa-cc-amex',
			'fab fa-cc-apple-pay',
			'fab fa-cc-diners-club',
			'fab fa-cc-discover',
			'fab fa-cc-jcb',
			'fab fa-cc-mastercard',
			'fab fa-cc-paypal',
			'fab fa-cc-stripe',
			'fab fa-cc-visa',
			'fab fa-centercode',
			'fab fa-centos',
			'fab fa-chrome',
			'fab fa-chromecast',
			'fab fa-cloudflare',
			'fab fa-cloudscale',
			'fab fa-cloudsmith',
			'fab fa-cloudversify',
			'fab fa-cmplid',
			'fab fa-codepen',
			'fab fa-codiepie',
			'fab fa-confluence',
			'fab fa-connectdevelop',
			'fab fa-contao',
			'fab fa-cotton-bureau',
			'fab fa-cpanel',
			'fab fa-creative-commons-by',
			'fab fa-creative-commons-nc-eu',
			'fab fa-creative-commons-nc-jp',
			'fab fa-creative-commons-nc',
			'fab fa-creative-commons-nd',
			'fab fa-creative-commons-pd-alt',
			'fab fa-creative-commons-pd',
			'fab fa-creative-commons-remix',
			'fab fa-creative-commons-sa',
			'fab fa-creative-commons-sampling-plus',
			'fab fa-creative-commons-sampling',
			'fab fa-creative-commons-share',
			'fab fa-creative-commons-zero',
			'fab fa-creative-commons',
			'fab fa-critical-role',
			'fab fa-css3-alt',
			'fab fa-css3',
			'fab fa-cuttlefish',
			'fab fa-d-and-d-beyond',
			'fab fa-d-and-d',
			'fab fa-dailymotion',
			'fab fa-dart-lang',
			'fab fa-dashcube',
			'fab fa-debian',
			'fab fa-deezer',
			'fab fa-delicious',
			'fab fa-deploydog',
			'fab fa-deskpro',
			'fab fa-dev',
			'fab fa-deviantart',
			'fab fa-dhl',
			'fab fa-diaspora',
			'fab fa-digg',
			'fab fa-digital-ocean',
			'fab fa-discord',
			'fab fa-discourse',
			'fab fa-dochub',
			'fab fa-docker',
			'fab fa-draft2digital',
			'fab fa-dribbble',
			'fab fa-dropbox',
			'fab fa-drupal',
			'fab fa-dyalog',
			'fab fa-earlybirds',
			'fab fa-ebay',
			'fab fa-edge-legacy',
			'fab fa-edge',
			'fab fa-elementor',
			'fab fa-ello',
			'fab fa-ember',
			'fab fa-empire',
			'fab fa-envira',
			'fab fa-erlang',
			'fab fa-ethereum',
			'fab fa-etsy',
			'fab fa-evernote',
			'fab fa-expeditedssl',
			'fab fa-facebook-f',
			'fab fa-facebook-messenger',
			'fab fa-facebook',
			'fab fa-fantasy-flight-games',
			'fab fa-fedex',
			'fab fa-fedora',
			'fab fa-figma',
			'fab fa-firefox-browser',
			'fab fa-firefox',
			'fab fa-first-order-alt',
			'fab fa-first-order',
			'fab fa-firstdraft',
			'fab fa-flickr',
			'fab fa-flipboard',
			'fab fa-flutter',
			'fab fa-fly',
			'fab fa-font-awesome',
			'fab fa-fonticons-fi',
			'fab fa-fonticons',
			'fab fa-fort-awesome-alt',
			'fab fa-fort-awesome',
			'fab fa-forumbee',
			'fab fa-foursquare',
			'fab fa-free-code-camp',
			'fab fa-freebsd',
			'fab fa-fulcrum',
			'fab fa-galactic-republic',
			'fab fa-galactic-senate',
			'fab fa-get-pocket',
			'fab fa-gg-circle',
			'fab fa-gg',
			'fab fa-git-alt',
			'fab fa-git',
			'fab fa-github-alt',
			'fab fa-github',
			'fab fa-gitkraken',
			'fab fa-gitlab',
			'fab fa-gitter',
			'fab fa-glide-g',
			'fab fa-glide',
			'fab fa-gofore',
			'fab fa-golang',
			'fab fa-goodreads-g',
			'fab fa-goodreads',
			'fab fa-google-drive',
			'fab fa-google-pay',
			'fab fa-google-play',
			'fab fa-google-plus-g',
			'fab fa-google-plus',
			'fab fa-google-scholar',
			'fab fa-google-wallet',
			'fab fa-google',
			'fab fa-gratipay',
			'fab fa-grav',
			'fab fa-gripfire',
			'fab fa-grunt',
			'fab fa-guilded',
			'fab fa-gulp',
			'fab fa-hacker-news',
			'fab fa-hackerrank',
			'fab fa-hashnode',
			'fab fa-hips',
			'fab fa-hire-a-helper',
			'fab fa-hive',
			'fab fa-hooli',
			'fab fa-hornbill',
			'fab fa-hotjar',
			'fab fa-houzz',
			'fab fa-html5',
			'fab fa-hubspot',
			'fab fa-ideal',
			'fab fa-imdb',
			'fab fa-instagram',
			'fab fa-instalod',
			'fab fa-intercom',
			'fab fa-internet-explorer',
			'fab fa-invision',
			'fab fa-ioxhost',
			'fab fa-itch-io',
			'fab fa-itunes-note',
			'fab fa-itunes',
			'fab fa-java',
			'fab fa-jedi-order',
			'fab fa-jenkins',
			'fab fa-jira',
			'fab fa-joget',
			'fab fa-joomla',
			'fab fa-js',
			'fab fa-jsfiddle',
			'fab fa-jxl',
			'fab fa-kaggle',
			'fab fa-keybase',
			'fab fa-keycdn',
			'fab fa-kickstarter-k',
			'fab fa-kickstarter',
			'fab fa-korvue',
			'fab fa-laravel',
			'fab fa-lastfm',
			'fab fa-leanpub',
			'fab fa-less',
			'fab fa-letterboxd',
			'fab fa-line',
			'fab fa-linkedin-in',
			'fab fa-linkedin',
			'fab fa-linode',
			'fab fa-linux',
			'fab fa-lyft',
			'fab fa-magento',
			'fab fa-mailchimp',
			'fab fa-mandalorian',
			'fab fa-markdown',
			'fab fa-mastodon',
			'fab fa-maxcdn',
			'fab fa-mdb',
			'fab fa-medapps',
			'fab fa-medium',
			'fab fa-medrt',
			'fab fa-meetup',
			'fab fa-megaport',
			'fab fa-mendeley',
			'fab fa-meta',
			'fab fa-microblog',
			'fab fa-microsoft',
			'fab fa-mintbit',
			'fab fa-mix',
			'fab fa-mixcloud',
			'fab fa-mixer',
			'fab fa-mizuni',
			'fab fa-modx',
			'fab fa-monero',
			'fab fa-napster',
			'fab fa-neos',
			'fab fa-nfc-directional',
			'fab fa-nfc-symbol',
			'fab fa-nimblr',
			'fab fa-node-js',
			'fab fa-node',
			'fab fa-npm',
			'fab fa-ns8',
			'fab fa-nutritionix',
			'fab fa-octopus-deploy',
			'fab fa-odnoklassniki',
			'fab fa-odysee',
			'fab fa-old-republic',
			'fab fa-opencart',
			'fab fa-openid',
			'fab fa-opensuse',
			'fab fa-opera',
			'fab fa-optin-monster',
			'fab fa-orcid',
			'fab fa-osi',
			'fab fa-padlet',
			'fab fa-page4',
			'fab fa-pagelines',
			'fab fa-palfed',
			'fab fa-patreon',
			'fab fa-paypal',
			'fab fa-perbyte',
			'fab fa-periscope',
			'fab fa-phabricator',
			'fab fa-phoenix-framework',
			'fab fa-phoenix-squadron',
			'fab fa-php',
			'fab fa-pied-piper-alt',
			'fab fa-pied-piper-hat',
			'fab fa-pied-piper-pp',
			'fab fa-pied-piper',
			'fab fa-pinterest-p',
			'fab fa-pinterest',
			'fab fa-pix',
			'fab fa-pixiv',
			'fab fa-playstation',
			'fab fa-product-hunt',
			'fab fa-pushed',
			'fab fa-python',
			'fab fa-qq',
			'fab fa-quinscape',
			'fab fa-quora',
			'fab fa-r-project',
			'fab fa-raspberry-pi',
			'fab fa-ravelry',
			'fab fa-react',
			'fab fa-reacteurope',
			'fab fa-readme',
			'fab fa-rebel',
			'fab fa-red-river',
			'fab fa-reddit-alien',
			'fab fa-reddit',
			'fab fa-redhat',
			'fab fa-renren',
			'fab fa-replyd',
			'fab fa-researchgate',
			'fab fa-resolving',
			'fab fa-rev',
			'fab fa-rocketchat',
			'fab fa-rockrms',
			'fab fa-rust',
			'fab fa-safari',
			'fab fa-salesforce',
			'fab fa-sass',
			'fab fa-schlix',
			'fab fa-screenpal',
			'fab fa-scribd',
			'fab fa-searchengin',
			'fab fa-sellcast',
			'fab fa-sellsy',
			'fab fa-servicestack',
			'fab fa-shirtsinbulk',
			'fab fa-shoelace',
			'fab fa-shopify',
			'fab fa-shopware',
			'fab fa-signal-messenger',
			'fab fa-simplybuilt',
			'fab fa-sistrix',
			'fab fa-sith',
			'fab fa-sitrox',
			'fab fa-sketch',
			'fab fa-skyatlas',
			'fab fa-skype',
			'fab fa-slack',
			'fab fa-slideshare',
			'fab fa-snapchat',
			'fab fa-soundcloud',
			'fab fa-sourcetree',
			'fab fa-space-awesome',
			'fab fa-speakap',
			'fab fa-speaker-deck',
			'fab fa-spotify',
			'fab fa-square-behance',
			'fab fa-square-dribbble',
			'fab fa-square-facebook',
			'fab fa-square-font-awesome-stroke',
			'fab fa-square-font-awesome',
			'fab fa-square-git',
			'fab fa-square-github',
			'fab fa-square-gitlab',
			'fab fa-square-google-plus',
			'fab fa-square-hacker-news',
			'fab fa-square-instagram',
			'fab fa-square-js',
			'fab fa-square-lastfm',
			'fab fa-square-letterboxd',
			'fab fa-square-odnoklassniki',
			'fab fa-square-pied-piper',
			'fab fa-square-pinterest',
			'fab fa-square-reddit',
			'fab fa-square-snapchat',
			'fab fa-square-steam',
			'fab fa-square-threads',
			'fab fa-square-tumblr',
			'fab fa-square-twitter',
			'fab fa-square-upwork',
			'fab fa-square-viadeo',
			'fab fa-square-vimeo',
			'fab fa-square-web-awesome-stroke',
			'fab fa-square-web-awesome',
			'fab fa-square-whatsapp',
			'fab fa-square-x-twitter',
			'fab fa-square-xing',
			'fab fa-square-youtube',
			'fab fa-squarespace',
			'fab fa-stack-exchange',
			'fab fa-stack-overflow',
			'fab fa-stackpath',
			'fab fa-staylinked',
			'fab fa-steam-symbol',
			'fab fa-steam',
			'fab fa-sticker-mule',
			'fab fa-strava',
			'fab fa-stripe-s',
			'fab fa-stripe',
			'fab fa-stubber',
			'fab fa-studiovinari',
			'fab fa-stumbleupon-circle',
			'fab fa-stumbleupon',
			'fab fa-superpowers',
			'fab fa-supple',
			'fab fa-suse',
			'fab fa-swift',
			'fab fa-symfony',
			'fab fa-teamspeak',
			'fab fa-telegram',
			'fab fa-tencent-weibo',
			'fab fa-the-red-yeti',
			'fab fa-themeco',
			'fab fa-themeisle',
			'fab fa-think-peaks',
			'fab fa-threads',
			'fab fa-tiktok',
			'fab fa-trade-federation',
			'fab fa-trello',
			'fab fa-tumblr',
			'fab fa-twitch',
			'fab fa-twitter',
			'fab fa-typo3',
			'fab fa-uber',
			'fab fa-ubuntu',
			'fab fa-uikit',
			'fab fa-umbraco',
			'fab fa-uncharted',
			'fab fa-uniregistry',
			'fab fa-unity',
			'fab fa-unsplash',
			'fab fa-untappd',
			'fab fa-ups',
			'fab fa-upwork',
			'fab fa-usb',
			'fab fa-usps',
			'fab fa-ussunnah',
			'fab fa-vaadin',
			'fab fa-viacoin',
			'fab fa-viadeo',
			'fab fa-viber',
			'fab fa-vimeo-v',
			'fab fa-vimeo',
			'fab fa-vine',
			'fab fa-vk',
			'fab fa-vnv',
			'fab fa-vuejs',
			'fab fa-watchman-monitoring',
			'fab fa-waze',
			'fab fa-web-awesome',
			'fab fa-webflow',
			'fab fa-weebly',
			'fab fa-weibo',
			'fab fa-weixin',
			'fab fa-whatsapp',
			'fab fa-whmcs',
			'fab fa-wikipedia-w',
			'fab fa-windows',
			'fab fa-wirsindhandwerk',
			'fab fa-wix',
			'fab fa-wizards-of-the-coast',
			'fab fa-wodu',
			'fab fa-wolf-pack-battalion',
			'fab fa-wordpress-simple',
			'fab fa-wordpress',
			'fab fa-wpbeginner',
			'fab fa-wpexplorer',
			'fab fa-wpforms',
			'fab fa-wpressr',
			'fab fa-x-twitter',
			'fab fa-xbox',
			'fab fa-xing',
			'fab fa-y-combinator',
			'fab fa-yahoo',
			'fab fa-yammer',
			'fab fa-yandex-international',
			'fab fa-yandex',
			'fab fa-yarn',
			'fab fa-yelp',
			'fab fa-yoast',
			'fab fa-youtube',
			'fab fa-zhihu',
		];

		return array_combine( $icons, $icons );
	}

	public static function user_roles(): array {
		$editable_role = array_reverse( get_editable_roles() );
		$users_arr     = array();
		foreach ( $editable_role as $role => $details ) {

			$name = translate_user_role( $details['name'] );
			$val  = [
				'type'  => 'checkbox',
				'title' => $name,
				'label' => __( 'Enable', 'button-generation' ),

			];

			$users_arr[ 'user_' . $role ] = $val;

		}

		return $users_arr;
	}

}
