/*--------------------------------

WpieIcon icon font
Generated using nucleoapp.com

-------------------------------- */

@wpie_icon-font-path: "../fonts";

@font-face {
  font-family: 'WpieIcon';
  src: url('@{wpie_icon-font-path}/WpieIcon.eot');
  src: url('@{wpie_icon-font-path}/WpieIcon.eot') format('embedded-opentype'),
       url('@{wpie_icon-font-path}/WpieIcon.woff2') format('woff2'),
       url('@{wpie_icon-font-path}/WpieIcon.woff') format('woff'),
       url('@{wpie_icon-font-path}/WpieIcon.ttf') format('truetype'),
       url('@{wpie_icon-font-path}/WpieIcon.svg') format('svg');
}

/* base class */
.wpie-icon {
  display: inline-block;
  font: normal normal normal 1em/1 'WpieIcon';
  color: inherit;
  flex-shrink: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* size examples - relative units */
.wpie-icon-sm {
	font-size: 0.8em;
}

.wpie-icon-lg {
  font-size: 1.2em;
}

/* size examples - absolute units */
.wpie-icon-16 {
  font-size: 16px;
}

.wpie-icon-32 {
  font-size: 32px;
}

/* rotate the icon infinitely */
.wpie-icon-is-spinning {
  animation: wpie-icon-spin 1s infinite linear;
}

@keyframes wpie-icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* transform */
.wpie-icon-rotate-90  {
	transform: rotate(90deg);
}

.wpie-icon-rotate-180 {
	transform: rotate(180deg);
}

.wpie-icon-rotate-270 {
	transform: rotate(270deg);
}

.wpie-icon-flip-y {
	transform: scaleY(-1);
}

.wpie-icon-flip-x {
	transform: scaleX(-1);
}

/* icons */
@wpie_icon-file-download: "\ea03";
@wpie_icon-plug: "\ea05";
@wpie_icon-trash: "\ea06";
@wpie_icon-xmark: "\ea07";
@wpie_icon-pointer: "\ea08";
@wpie_icon-ruler-pen: "\ea09";
@wpie_icon-paintbrush: "\ea0a";
@wpie_icon-play: "\ea0b";
@wpie_icon-buttons: "\ea0c";
@wpie_icon-users: "\ea0d";
@wpie_icon-text: "\ea0e";
@wpie_icon-laptop-mobile: "\ea0f";
@wpie_icon-arrow-bottom: "\ea10";
@wpie_icon-globe-pointer: "\ea11";
@wpie_icon-square-plus: "\ea12";
@wpie_icon-plus: "\ea13";
@wpie_icon-calendar: "\ea14";
@wpie_icon-grid-circle-plus: "\ea15";
@wpie_icon-gear: "\ea16";
@wpie_icon-check: "\ea17";
@wpie_icon-chart-line: "\ea19";
@wpie_icon-chart: "\ea1a";
@wpie_icon-link: "\ea1b";
@wpie_icon-target: "\ea1c";
@wpie_icon-sparkle: "\ea1d";
@wpie_icon-laptop: "\ea1e";
@wpie_icon-paperclip: "\ea1f";
@wpie_icon-at-sign: "\ea20";
@wpie_icon-crosshairs: "\ea21";
@wpie_icon-lock: "\ea22";
@wpie_icon-lock-open: "\ea23";
@wpie_icon-chevron-up: "\ea24";
@wpie_icon-chevron-down: "\ea25";
@wpie_icon-roadmap: "\ea28";
@wpie_icon-tag: "\ea29";
@wpie_icon-square-minus: "\ea2a";
@wpie_icon-bottom: "\ea2b";
@wpie_icon-envelope: "\ea2c";
@wpie_icon-user: "\ea2d";
@wpie_icon-key: "\ea2e";
@wpie_icon-border-width: "\ea2f";
@wpie_icon-eye-open: "\ea30";
@wpie_icon-award: "\ea31";
@wpie_icon-newsletter: "\ea32";
@wpie_icon-copy: "\ea33";
@wpie_icon-file-content: "\ea34";
@wpie_icon-rocket: "\ea35";
@wpie_icon-filter: "\ea36";

.wpie_icon-file-download::before {
  content: @wpie_icon-file-download;
}

.wpie_icon-plug::before {
  content: @wpie_icon-plug;
}

.wpie_icon-trash::before {
  content: @wpie_icon-trash;
}

.wpie_icon-xmark::before {
  content: @wpie_icon-xmark;
}

.wpie_icon-pointer::before {
  content: @wpie_icon-pointer;
}

.wpie_icon-ruler-pen::before {
  content: @wpie_icon-ruler-pen;
}

.wpie_icon-paintbrush::before {
  content: @wpie_icon-paintbrush;
}

.wpie_icon-play::before {
  content: @wpie_icon-play;
}

.wpie_icon-buttons::before {
  content: @wpie_icon-buttons;
}

.wpie_icon-users::before {
  content: @wpie_icon-users;
}

.wpie_icon-text::before {
  content: @wpie_icon-text;
}

.wpie_icon-laptop-mobile::before {
  content: @wpie_icon-laptop-mobile;
}

.wpie_icon-arrow-bottom::before {
  content: @wpie_icon-arrow-bottom;
}

.wpie_icon-globe-pointer::before {
  content: @wpie_icon-globe-pointer;
}

.wpie_icon-square-plus::before {
  content: @wpie_icon-square-plus;
}

.wpie_icon-plus::before {
  content: @wpie_icon-plus;
}

.wpie_icon-calendar::before {
  content: @wpie_icon-calendar;
}

.wpie_icon-grid-circle-plus::before {
  content: @wpie_icon-grid-circle-plus;
}

.wpie_icon-gear::before {
  content: @wpie_icon-gear;
}

.wpie_icon-check::before {
  content: @wpie_icon-check;
}

.wpie_icon-chart-line::before {
  content: @wpie_icon-chart-line;
}

.wpie_icon-chart::before {
  content: @wpie_icon-chart;
}

.wpie_icon-link::before {
  content: @wpie_icon-link;
}

.wpie_icon-target::before {
  content: @wpie_icon-target;
}

.wpie_icon-sparkle::before {
  content: @wpie_icon-sparkle;
}

.wpie_icon-laptop::before {
  content: @wpie_icon-laptop;
}

.wpie_icon-paperclip::before {
  content: @wpie_icon-paperclip;
}

.wpie_icon-at-sign::before {
  content: @wpie_icon-at-sign;
}

.wpie_icon-crosshairs::before {
  content: @wpie_icon-crosshairs;
}

.wpie_icon-lock::before {
  content: @wpie_icon-lock;
}

.wpie_icon-lock-open::before {
  content: @wpie_icon-lock-open;
}

.wpie_icon-chevron-up::before {
  content: @wpie_icon-chevron-up;
}

.wpie_icon-chevron-down::before {
  content: @wpie_icon-chevron-down;
}

.wpie_icon-roadmap::before {
  content: @wpie_icon-roadmap;
}

.wpie_icon-tag::before {
  content: @wpie_icon-tag;
}

.wpie_icon-square-minus::before {
  content: @wpie_icon-square-minus;
}

.wpie_icon-bottom::before {
  content: @wpie_icon-bottom;
}

.wpie_icon-envelope::before {
  content: @wpie_icon-envelope;
}

.wpie_icon-user::before {
  content: @wpie_icon-user;
}

.wpie_icon-key::before {
  content: @wpie_icon-key;
}

.wpie_icon-border-width::before {
  content: @wpie_icon-border-width;
}

.wpie_icon-eye-open::before {
  content: @wpie_icon-eye-open;
}

.wpie_icon-award::before {
  content: @wpie_icon-award;
}

.wpie_icon-newsletter::before {
  content: @wpie_icon-newsletter;
}

.wpie_icon-copy::before {
  content: @wpie_icon-copy;
}

.wpie_icon-file-content::before {
  content: @wpie_icon-file-content;
}

.wpie_icon-rocket::before {
  content: @wpie_icon-rocket;
}

.wpie_icon-filter::before {
  content: @wpie_icon-filter;
}

