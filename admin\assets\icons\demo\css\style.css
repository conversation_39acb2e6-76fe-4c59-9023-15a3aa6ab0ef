/*--------------------------------

WpieIcon Web Font
Generated using nucleoapp.com

-------------------------------- */
@font-face {
  font-family: 'WpieIcon';
  src: url('../../fonts/WpieIcon.eot');
  src: url('../../fonts/WpieIcon.eot') format('embedded-opentype'), url('../../fonts/WpieIcon.woff2') format('woff2'), url('../../fonts/WpieIcon.woff') format('woff'), url('../../fonts/WpieIcon.ttf') format('truetype'), url('../../fonts/WpieIcon.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

/* --------------------------------

Reset

-------------------------------- */

*, *::after, *::before {
  box-sizing: inherit;
}

* {
  font: inherit;
}

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video, hr {
  margin: 0;
  padding: 0;
  border: 0;
}

html {
  box-sizing: border-box;
}

body {
  background-color: white;
  font-family: system-ui, sans-serif;
  color: hsl(240, 4%, 20%);
  padding: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section, main, form legend {
  display: block;
}

ol, ul {
  list-style: none;
}

button, input, textarea, select {
  margin: 0;
}

a {
  color: hsl(230, 93%, 66%);
}

/* --------------------------------

Main components

-------------------------------- */
header {
  text-align: center;
  margin: 3em auto;
}

header h1 {
  font-size: 2.6rem;
  font-weight: 600;
}

header p {
  font-size: 1rem;
  margin-top: 1em;
  color: hsla(0, 0%, 0%, 0.5);
}

ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

ul li {
  border-radius: .4em;
  transition: background-color .2s;
  user-select: none;
  overflow: hidden;
  text-align: center;
  padding: 1em;
}

ul li:hover {
  background: hsla(0, 0%, 0%, 0.05);
}

ul p, ul em, ul input {
  display: block;
  font-size: 0.75rem;
  color: hsla(0, 0%, 0%, 0.5);
  user-select: auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  max-width: 6rem;
}

ul p {
  padding: 8px 0 4px;
}

ul p::selection, ul em::selection {
  background: hsl(230, 93%, 66%);
  color: #fff;
}

ul p::-moz-selection, ul em::-moz-selection {
  background: hsl(230, 93%, 66%);
  color: #fff;
}

ul em {
  margin-bottom: 4px;
}

ul em::before {
  content: '[';
}
ul em::after {
  content: ']';
}

ul input {
  text-align: center;
  background: transparent;
  border: none;
  box-shadow: none;
  outline: none;
  font-family: auto;
}

/* --------------------------------

icons

-------------------------------- */
.wpie-icon {
  display: inline-block;
  font: normal normal normal 32px/1 'WpieIcon';
  speak: none;
  text-transform: none;
  /* Better Font Rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*------------------------
  font icons
-------------------------*/

.wpie_icon-file-download::before {
  content: "\ea03";
}

.wpie_icon-plug::before {
  content: "\ea05";
}

.wpie_icon-trash::before {
  content: "\ea06";
}

.wpie_icon-xmark::before {
  content: "\ea07";
}

.wpie_icon-pointer::before {
  content: "\ea08";
}

.wpie_icon-ruler-pen::before {
  content: "\ea09";
}

.wpie_icon-paintbrush::before {
  content: "\ea0a";
}

.wpie_icon-play::before {
  content: "\ea0b";
}

.wpie_icon-buttons::before {
  content: "\ea0c";
}

.wpie_icon-users::before {
  content: "\ea0d";
}

.wpie_icon-text::before {
  content: "\ea0e";
}

.wpie_icon-laptop-mobile::before {
  content: "\ea0f";
}

.wpie_icon-arrow-bottom::before {
  content: "\ea10";
}

.wpie_icon-globe-pointer::before {
  content: "\ea11";
}

.wpie_icon-square-plus::before {
  content: "\ea12";
}

.wpie_icon-plus::before {
  content: "\ea13";
}

.wpie_icon-calendar::before {
  content: "\ea14";
}

.wpie_icon-grid-circle-plus::before {
  content: "\ea15";
}

.wpie_icon-gear::before {
  content: "\ea16";
}

.wpie_icon-check::before {
  content: "\ea17";
}

.wpie_icon-chart-line::before {
  content: "\ea19";
}

.wpie_icon-chart::before {
  content: "\ea1a";
}

.wpie_icon-link::before {
  content: "\ea1b";
}

.wpie_icon-target::before {
  content: "\ea1c";
}

.wpie_icon-sparkle::before {
  content: "\ea1d";
}

.wpie_icon-laptop::before {
  content: "\ea1e";
}

.wpie_icon-paperclip::before {
  content: "\ea1f";
}

.wpie_icon-at-sign::before {
  content: "\ea20";
}

.wpie_icon-crosshairs::before {
  content: "\ea21";
}

.wpie_icon-lock::before {
  content: "\ea22";
}

.wpie_icon-lock-open::before {
  content: "\ea23";
}

.wpie_icon-chevron-up::before {
  content: "\ea24";
}

.wpie_icon-chevron-down::before {
  content: "\ea25";
}

.wpie_icon-roadmap::before {
  content: "\ea28";
}

.wpie_icon-tag::before {
  content: "\ea29";
}

.wpie_icon-square-minus::before {
  content: "\ea2a";
}

.wpie_icon-bottom::before {
  content: "\ea2b";
}

.wpie_icon-envelope::before {
  content: "\ea2c";
}

.wpie_icon-user::before {
  content: "\ea2d";
}

.wpie_icon-key::before {
  content: "\ea2e";
}

.wpie_icon-border-width::before {
  content: "\ea2f";
}

.wpie_icon-eye-open::before {
  content: "\ea30";
}

.wpie_icon-award::before {
  content: "\ea31";
}

.wpie_icon-newsletter::before {
  content: "\ea32";
}

.wpie_icon-copy::before {
  content: "\ea33";
}

.wpie_icon-file-content::before {
  content: "\ea34";
}

.wpie_icon-rocket::before {
  content: "\ea35";
}

.wpie_icon-filter::before {
  content: "\ea36";
}

