#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Button Generator\n"
"POT-Creation-Date: 2025-03-18 11:20+0200\n"
"PO-Revision-Date: 2024-04-08 15:53+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: button-generation.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;"
"_n_noop:1,2;_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#: admin/class-settings-helper.php:24 admin/settings/options/settings.php:87
msgid "Link"
msgstr ""

#: admin/class-settings-helper.php:25
msgid "Login"
msgstr ""

#: admin/class-settings-helper.php:26
msgid "Logout"
msgstr ""

#: admin/class-settings-helper.php:27
msgid "Lostpassword"
msgstr ""

#: admin/class-settings-helper.php:28
msgid "Register"
msgstr ""

#: admin/class-settings-helper.php:35
msgid "Facebook"
msgstr ""

#: admin/class-settings-helper.php:36
msgid "Twitter"
msgstr ""

#: admin/class-settings-helper.php:37
msgid "Linkedin"
msgstr ""

#: admin/class-settings-helper.php:38
msgid "Pinterest"
msgstr ""

#: admin/class-settings-helper.php:39
msgid "StumbleUpon"
msgstr ""

#: admin/class-settings-helper.php:40
msgid "Buffer"
msgstr ""

#: admin/class-settings-helper.php:41
msgid "Email"
msgstr ""

#: admin/class-settings-helper.php:42
msgid "XING"
msgstr ""

#: admin/class-settings-helper.php:43
msgid "Myspace"
msgstr ""

#: admin/class-settings-helper.php:44
msgid "Weibo"
msgstr ""

#: admin/class-settings-helper.php:45
msgid "Reddit"
msgstr ""

#: admin/class-settings-helper.php:46
msgid "Tumblr"
msgstr ""

#: admin/class-settings-helper.php:47
msgid "Blogger"
msgstr ""

#: admin/class-settings-helper.php:48
msgid "LiveJournal"
msgstr ""

#: admin/class-settings-helper.php:49
msgid "Pocket"
msgstr ""

#: admin/class-settings-helper.php:50
msgid "Telegram"
msgstr ""

#: admin/class-settings-helper.php:51
msgid "Skype"
msgstr ""

#: admin/class-settings-helper.php:52
msgid "Draugiem"
msgstr ""

#: admin/class-settings-helper.php:53
msgid "Whatsapp"
msgstr ""

#: admin/class-settings-helper.php:54
msgid "Diaspora"
msgstr ""

#: admin/class-settings-helper.php:55
msgid "Digg"
msgstr ""

#: admin/class-settings-helper.php:56
msgid "Douban"
msgstr ""

#: admin/class-settings-helper.php:57
msgid "Evernote"
msgstr ""

#: admin/class-settings-helper.php:58
msgid "Flipboard"
msgstr ""

#: admin/class-settings-helper.php:59
msgid "Hacker News"
msgstr ""

#: admin/class-settings-helper.php:60
msgid "Instapaper"
msgstr ""

#: admin/class-settings-helper.php:61
msgid "Line"
msgstr ""

#: admin/class-settings-helper.php:62
msgid "Qzone"
msgstr ""

#: admin/class-settings-helper.php:63
msgid "Renren"
msgstr ""

#: admin/class-settings-helper.php:69
msgid "Afrikaans"
msgstr ""

#: admin/class-settings-helper.php:70
msgid "Albanian"
msgstr ""

#: admin/class-settings-helper.php:71
msgid "Amharic"
msgstr ""

#: admin/class-settings-helper.php:72
msgid "Arabic"
msgstr ""

#: admin/class-settings-helper.php:73
msgid "Armenian"
msgstr ""

#: admin/class-settings-helper.php:74
msgid "Azerbaijani"
msgstr ""

#: admin/class-settings-helper.php:75
msgid "Basque"
msgstr ""

#: admin/class-settings-helper.php:76
msgid "Belarusian"
msgstr ""

#: admin/class-settings-helper.php:77
msgid "Bengali"
msgstr ""

#: admin/class-settings-helper.php:78
msgid "Bosnian"
msgstr ""

#: admin/class-settings-helper.php:79
msgid "Bulgarian"
msgstr ""

#: admin/class-settings-helper.php:80
msgid "Catalan"
msgstr ""

#: admin/class-settings-helper.php:81
msgid "Cebuano"
msgstr ""

#: admin/class-settings-helper.php:82
msgid "Chichewa"
msgstr ""

#: admin/class-settings-helper.php:83
msgid "Corsican"
msgstr ""

#: admin/class-settings-helper.php:84
msgid "Croatian"
msgstr ""

#: admin/class-settings-helper.php:85
msgid "Czech"
msgstr ""

#: admin/class-settings-helper.php:86
msgid "Danish"
msgstr ""

#: admin/class-settings-helper.php:87
msgid "Dutch"
msgstr ""

#: admin/class-settings-helper.php:88
msgid "English"
msgstr ""

#: admin/class-settings-helper.php:89
msgid "Esperanto"
msgstr ""

#: admin/class-settings-helper.php:90
msgid "Estonian"
msgstr ""

#: admin/class-settings-helper.php:91
msgid "Filipino"
msgstr ""

#: admin/class-settings-helper.php:92
msgid "Finnish"
msgstr ""

#: admin/class-settings-helper.php:93
msgid "French"
msgstr ""

#: admin/class-settings-helper.php:94
msgid "Frisian"
msgstr ""

#: admin/class-settings-helper.php:95
msgid "Galician"
msgstr ""

#: admin/class-settings-helper.php:96
msgid "Georgian"
msgstr ""

#: admin/class-settings-helper.php:97
msgid "German"
msgstr ""

#: admin/class-settings-helper.php:98
msgid "Greek"
msgstr ""

#: admin/class-settings-helper.php:99
msgid "Gujarati"
msgstr ""

#: admin/class-settings-helper.php:100
msgid "Haitian Creole"
msgstr ""

#: admin/class-settings-helper.php:101
msgid "Hausa"
msgstr ""

#: admin/class-settings-helper.php:102
msgid "Hawaiian"
msgstr ""

#: admin/class-settings-helper.php:103
msgid "Hebrew"
msgstr ""

#: admin/class-settings-helper.php:104
msgid "Hindi"
msgstr ""

#: admin/class-settings-helper.php:105
msgid "Hmong"
msgstr ""

#: admin/class-settings-helper.php:106
msgid "Hungarian"
msgstr ""

#: admin/class-settings-helper.php:107
msgid "Icelandic"
msgstr ""

#: admin/class-settings-helper.php:108
msgid "Igbo"
msgstr ""

#: admin/class-settings-helper.php:109
msgid "Indonesian"
msgstr ""

#: admin/class-settings-helper.php:110
msgid "Irish"
msgstr ""

#: admin/class-settings-helper.php:111
msgid "Italian"
msgstr ""

#: admin/class-settings-helper.php:112
msgid "Japanese"
msgstr ""

#: admin/class-settings-helper.php:113
msgid "Javanese"
msgstr ""

#: admin/class-settings-helper.php:114
msgid "Kannada"
msgstr ""

#: admin/class-settings-helper.php:115
msgid "Kazakh"
msgstr ""

#: admin/class-settings-helper.php:116
msgid "Khmer"
msgstr ""

#: admin/class-settings-helper.php:117
msgid "Korean"
msgstr ""

#: admin/class-settings-helper.php:118
msgid "Kurdish (Kurmanji)"
msgstr ""

#: admin/class-settings-helper.php:119
msgid "Kyrgyz"
msgstr ""

#: admin/class-settings-helper.php:120
msgid "Lao"
msgstr ""

#: admin/class-settings-helper.php:121
msgid "Latin"
msgstr ""

#: admin/class-settings-helper.php:122
msgid "Latvian"
msgstr ""

#: admin/class-settings-helper.php:123
msgid "Luxembourgish"
msgstr ""

#: admin/class-settings-helper.php:124
msgid "Macedonian"
msgstr ""

#: admin/class-settings-helper.php:125
msgid "Malagasy"
msgstr ""

#: admin/class-settings-helper.php:126
msgid "Malay"
msgstr ""

#: admin/class-settings-helper.php:127
msgid "Malayalam"
msgstr ""

#: admin/class-settings-helper.php:128
msgid "Maltese"
msgstr ""

#: admin/class-settings-helper.php:129
msgid "Maori"
msgstr ""

#: admin/class-settings-helper.php:130
msgid "Marathi"
msgstr ""

#: admin/class-settings-helper.php:131
msgid "Mongolian"
msgstr ""

#: admin/class-settings-helper.php:132
msgid "Myanmar (Burmese)"
msgstr ""

#: admin/class-settings-helper.php:133
msgid "Nepali"
msgstr ""

#: admin/class-settings-helper.php:134
msgid "Norwegian"
msgstr ""

#: admin/class-settings-helper.php:135
msgid "Pashto"
msgstr ""

#: admin/class-settings-helper.php:136
msgid "Persian"
msgstr ""

#: admin/class-settings-helper.php:137
msgid "Polish"
msgstr ""

#: admin/class-settings-helper.php:138
msgid "Portuguese"
msgstr ""

#: admin/class-settings-helper.php:139
msgid "Punjabi"
msgstr ""

#: admin/class-settings-helper.php:140
msgid "Romanian"
msgstr ""

#: admin/class-settings-helper.php:141
msgid "Russian"
msgstr ""

#: admin/class-settings-helper.php:142
msgid "Samoan"
msgstr ""

#: admin/class-settings-helper.php:143
msgid "Scottish Gaelic"
msgstr ""

#: admin/class-settings-helper.php:144
msgid "Serbian"
msgstr ""

#: admin/class-settings-helper.php:145
msgid "Sesotho"
msgstr ""

#: admin/class-settings-helper.php:146
msgid "Shona"
msgstr ""

#: admin/class-settings-helper.php:147
msgid "Sindhi"
msgstr ""

#: admin/class-settings-helper.php:148
msgid "Sinhala"
msgstr ""

#: admin/class-settings-helper.php:149
msgid "Slovak"
msgstr ""

#: admin/class-settings-helper.php:150
msgid "Slovenian"
msgstr ""

#: admin/class-settings-helper.php:151
msgid "Somali"
msgstr ""

#: admin/class-settings-helper.php:152
msgid "Spanish"
msgstr ""

#: admin/class-settings-helper.php:153
msgid "Sudanese"
msgstr ""

#: admin/class-settings-helper.php:154
msgid "Swahili"
msgstr ""

#: admin/class-settings-helper.php:155
msgid "Swedish"
msgstr ""

#: admin/class-settings-helper.php:156
msgid "Tajik"
msgstr ""

#: admin/class-settings-helper.php:157
msgid "Tamil"
msgstr ""

#: admin/class-settings-helper.php:158
msgid "Telugu"
msgstr ""

#: admin/class-settings-helper.php:159
msgid "Thai"
msgstr ""

#: admin/class-settings-helper.php:160
msgid "Turkish"
msgstr ""

#: admin/class-settings-helper.php:161
msgid "Ukrainian"
msgstr ""

#: admin/class-settings-helper.php:162
msgid "Urdu"
msgstr ""

#: admin/class-settings-helper.php:163
msgid "Uzbek"
msgstr ""

#: admin/class-settings-helper.php:164
msgid "Vietnamese"
msgstr ""

#: admin/class-settings-helper.php:165
msgid "Welsh"
msgstr ""

#: admin/class-settings-helper.php:166
msgid "Xhosa"
msgstr ""

#: admin/class-settings-helper.php:167
msgid "Yiddish"
msgstr ""

#: admin/class-settings-helper.php:168
msgid "Yoruba"
msgstr ""

#: admin/class-settings-helper.php:169
msgid "Zulu"
msgstr ""

#: admin/class-settings-helper.php:298 admin/class-settings-helper.php:334
#: admin/class-settings-helper.php:366 admin/class-settings-helper.php:399
#: admin/class-settings-helper.php:421 admin/settings/options/settings.php:64
#: admin/settings/options/style.php:13 admin/settings/options/style.php:137
#: admin/settings/options/style.php:179
msgid "None"
msgstr ""

#: admin/class-settings-helper.php:299
msgid "Grow"
msgstr ""

#: admin/class-settings-helper.php:300
msgid "Shrink"
msgstr ""

#: admin/class-settings-helper.php:301
msgid "Pulse"
msgstr ""

#: admin/class-settings-helper.php:302
msgid "Pulse Grow"
msgstr ""

#: admin/class-settings-helper.php:303
msgid "Pulse Shrink"
msgstr ""

#: admin/class-settings-helper.php:304
msgid "Push"
msgstr ""

#: admin/class-settings-helper.php:305
msgid "Pop"
msgstr ""

#: admin/class-settings-helper.php:306
msgid "Bounce In"
msgstr ""

#: admin/class-settings-helper.php:307
msgid "Bounce Out"
msgstr ""

#: admin/class-settings-helper.php:308
msgid "Rotate"
msgstr ""

#: admin/class-settings-helper.php:309
msgid "Grow Rotate"
msgstr ""

#: admin/class-settings-helper.php:310
msgid "Float"
msgstr ""

#: admin/class-settings-helper.php:311
msgid "Sink"
msgstr ""

#: admin/class-settings-helper.php:312
msgid "Bob"
msgstr ""

#: admin/class-settings-helper.php:313
msgid "Hang"
msgstr ""

#: admin/class-settings-helper.php:314
msgid "Skew"
msgstr ""

#: admin/class-settings-helper.php:315
msgid "Skew Forward"
msgstr ""

#: admin/class-settings-helper.php:316
msgid "Skew Backward"
msgstr ""

#: admin/class-settings-helper.php:317
msgid "Wobble Horizontal"
msgstr ""

#: admin/class-settings-helper.php:318
msgid "Wobble Vertical"
msgstr ""

#: admin/class-settings-helper.php:319
msgid "Wobble To Bottom Right"
msgstr ""

#: admin/class-settings-helper.php:320
msgid "Wobble To Top Right"
msgstr ""

#: admin/class-settings-helper.php:321
msgid "Wobble Top"
msgstr ""

#: admin/class-settings-helper.php:322
msgid "Wobble Bottom"
msgstr ""

#: admin/class-settings-helper.php:323
msgid "Wobble Skew"
msgstr ""

#: admin/class-settings-helper.php:324
msgid "Buzz"
msgstr ""

#: admin/class-settings-helper.php:325
msgid "Buzz Out"
msgstr ""

#: admin/class-settings-helper.php:326
msgid "Forward"
msgstr ""

#: admin/class-settings-helper.php:327
msgid "Backward"
msgstr ""

#: admin/class-settings-helper.php:335
msgid "Background Transitions"
msgstr ""

#: admin/class-settings-helper.php:336
msgid "Back Pulse"
msgstr ""

#: admin/class-settings-helper.php:337
msgid "Sweep To Right"
msgstr ""

#: admin/class-settings-helper.php:338
msgid "Sweep To Left"
msgstr ""

#: admin/class-settings-helper.php:339
msgid "Sweep To Bottom"
msgstr ""

#: admin/class-settings-helper.php:340
msgid "Sweep To Top"
msgstr ""

#: admin/class-settings-helper.php:341
msgid "Bounce To Right"
msgstr ""

#: admin/class-settings-helper.php:342
msgid "Bounce To Left"
msgstr ""

#: admin/class-settings-helper.php:343
msgid "Bounce To Bottom"
msgstr ""

#: admin/class-settings-helper.php:344
msgid "Bounce To Top"
msgstr ""

#: admin/class-settings-helper.php:345
msgid "Radial Out"
msgstr ""

#: admin/class-settings-helper.php:346
msgid "Radial In"
msgstr ""

#: admin/class-settings-helper.php:347
msgid "Rectangle Out"
msgstr ""

#: admin/class-settings-helper.php:348
msgid "Rectangle In"
msgstr ""

#: admin/class-settings-helper.php:349
msgid "Shutter Out Horizontal"
msgstr ""

#: admin/class-settings-helper.php:350
msgid "Shutter In Horizontal"
msgstr ""

#: admin/class-settings-helper.php:351
msgid "Shutter Out Vertical"
msgstr ""

#: admin/class-settings-helper.php:352
msgid "Shutter In Vertical"
msgstr ""

#: admin/class-settings-helper.php:354
msgid "Curls"
msgstr ""

#: admin/class-settings-helper.php:355
msgid "Curl Top Left"
msgstr ""

#: admin/class-settings-helper.php:356
msgid "Curl Top Right"
msgstr ""

#: admin/class-settings-helper.php:357
msgid "Curl Bottom Right"
msgstr ""

#: admin/class-settings-helper.php:358
msgid "Curl Bottom Left"
msgstr ""

#: admin/class-settings-helper.php:367
msgid "Icon Back"
msgstr ""

#: admin/class-settings-helper.php:368
msgid "Icon Forward"
msgstr ""

#: admin/class-settings-helper.php:369
msgid "Icon Down"
msgstr ""

#: admin/class-settings-helper.php:370
msgid "Icon Up"
msgstr ""

#: admin/class-settings-helper.php:371
msgid "Icon Spin"
msgstr ""

#: admin/class-settings-helper.php:372
msgid "Icon Drop"
msgstr ""

#: admin/class-settings-helper.php:373
msgid "Icon Fade"
msgstr ""

#: admin/class-settings-helper.php:374
msgid "Icon Float Away"
msgstr ""

#: admin/class-settings-helper.php:375
msgid "Icon Sink Away"
msgstr ""

#: admin/class-settings-helper.php:376
msgid "Icon Grow"
msgstr ""

#: admin/class-settings-helper.php:377
msgid "Icon Shrink"
msgstr ""

#: admin/class-settings-helper.php:378
msgid "Icon Pulse"
msgstr ""

#: admin/class-settings-helper.php:379
msgid "Icon Pulse Grow"
msgstr ""

#: admin/class-settings-helper.php:380
msgid "Icon Pulse Shrink"
msgstr ""

#: admin/class-settings-helper.php:381
msgid "Icon Push"
msgstr ""

#: admin/class-settings-helper.php:382
msgid "Icon Pop"
msgstr ""

#: admin/class-settings-helper.php:383
msgid "Icon Bounce"
msgstr ""

#: admin/class-settings-helper.php:384
msgid "Icon Rotate"
msgstr ""

#: admin/class-settings-helper.php:385
msgid "Icon Grow Rotate"
msgstr ""

#: admin/class-settings-helper.php:386
msgid "Icon Float"
msgstr ""

#: admin/class-settings-helper.php:387
msgid "Icon Sink"
msgstr ""

#: admin/class-settings-helper.php:388
msgid "Icon Bob"
msgstr ""

#: admin/class-settings-helper.php:389
msgid "Icon Hang"
msgstr ""

#: admin/class-settings-helper.php:390
msgid "Icon Wobble Horizontal"
msgstr ""

#: admin/class-settings-helper.php:391
msgid "Icon Wobble Vertical"
msgstr ""

#: admin/class-settings-helper.php:392
msgid "Icon Buzz"
msgstr ""

#: admin/class-settings-helper.php:393
msgid "Icon Buzz Out"
msgstr ""

#: admin/class-settings-helper.php:400
msgid "Trim"
msgstr ""

#: admin/class-settings-helper.php:401
msgid "Ripple Out"
msgstr ""

#: admin/class-settings-helper.php:402
msgid "Ripple In"
msgstr ""

#: admin/class-settings-helper.php:403
msgid "Outline Out"
msgstr ""

#: admin/class-settings-helper.php:404
msgid "Outline In"
msgstr ""

#: admin/class-settings-helper.php:405
msgid "Round Corners"
msgstr ""

#: admin/class-settings-helper.php:406
msgid "Underline From Left"
msgstr ""

#: admin/class-settings-helper.php:407
msgid "Underline From Center"
msgstr ""

#: admin/class-settings-helper.php:408
msgid "Underline From Right"
msgstr ""

#: admin/class-settings-helper.php:409
msgid "Overline From Left"
msgstr ""

#: admin/class-settings-helper.php:410
msgid "Overline From Center"
msgstr ""

#: admin/class-settings-helper.php:411
msgid "Overline From Right"
msgstr ""

#: admin/class-settings-helper.php:412
msgid "Reveal"
msgstr ""

#: admin/class-settings-helper.php:413
msgid "Overline Reveal"
msgstr ""

#: admin/class-settings-helper.php:414
msgid "Underline Reveal"
msgstr ""

#: admin/class-settings-helper.php:422
msgid "Beat"
msgstr ""

#: admin/class-settings-helper.php:423
msgid "Fade"
msgstr ""

#: admin/class-settings-helper.php:424
msgid "Beat-Fade"
msgstr ""

#: admin/class-settings-helper.php:425
msgid "Bounce"
msgstr ""

#: admin/class-settings-helper.php:426
msgid "Flip"
msgstr ""

#: admin/class-settings-helper.php:427
msgid "Shake"
msgstr ""

#: admin/class-settings-helper.php:428
msgid "Spin"
msgstr ""

#: admin/class-settings-helper.php:2499
msgid "Enable"
msgstr ""

#: admin/pages/1.list.php:50
msgid "Search"
msgstr ""

#: admin/pages/2.settings.php:32
msgid "Enter title here"
msgstr ""

#: admin/pages/2.settings.php:35
msgid "Add title"
msgstr ""

#: admin/pages/2.settings.php:42
msgid "Live preview"
msgstr ""

#: admin/pages/3.tools.php:18
msgid "Export Settings"
msgstr ""

#. translators: %s: plugin name
#: admin/pages/3.tools.php:22
#, php-format
msgid ""
"Export the  settings for %s as a .json file. This allows you to easily "
"import the configuration into another site."
msgstr ""

#: admin/pages/3.tools.php:30
msgid "Import Settings"
msgstr ""

#. translators: %s: plugin name
#: admin/pages/3.tools.php:34
#, php-format
msgid ""
"Import the %s settings from a .json file. This file can be obtained by "
"exporting the settings on another site using the form above."
msgstr ""

#: admin/pages/4.support.php:17
msgid ""
"To get your support related question answered in the fastest timing, please "
"send a message via the form below or write to us via"
msgstr ""

#: admin/pages/4.support.php:18
msgid "support page"
msgstr ""

#: admin/pages/4.support.php:23
msgid ""
"Also, you can send us your ideas and suggestions for improving the plugin."
msgstr ""

#: admin/settings/1.settings.php:23
msgid "Content"
msgstr ""

#: admin/settings/1.settings.php:64 admin/settings/options/rules.php:77
msgid "Type"
msgstr ""

#: admin/settings/1.settings.php:83
msgid "Attributes"
msgstr ""

#: admin/settings/2.style.php:23
msgid "Sizes"
msgstr ""

#: admin/settings/2.style.php:31
msgid "Colors"
msgstr ""

#: admin/settings/2.style.php:45
msgid "Border"
msgstr ""

#: admin/settings/2.style.php:55
msgid "Drop Shadow"
msgstr ""

#: admin/settings/2.style.php:66
msgid "Font"
msgstr ""

#: admin/settings/5.rules.php:18
msgid "Button Type"
msgstr ""

#: admin/settings/5.rules.php:42
msgid "Display Rules"
msgstr ""

#: admin/settings/5.rules.php:75
msgid "Responsive Visibility"
msgstr ""

#: admin/settings/5.rules.php:105
msgid "Other"
msgstr ""

#: admin/settings/options/effects.php:12
msgid "Transition Duration"
msgstr ""

#: admin/settings/options/effects.php:23
msgid "Transition Function"
msgstr ""

#: admin/settings/options/rules.php:9 admin/settings/options/rules.php:12
msgid "General"
msgstr ""

#: admin/settings/options/rules.php:10 classes/Admin/ListTable.php:111
msgid "Shortcode"
msgstr ""

#: admin/settings/options/rules.php:11
msgid "Everywhere"
msgstr ""

#: admin/settings/options/rules.php:13
msgid "Posts"
msgstr ""

#: admin/settings/options/rules.php:14
msgid "All posts"
msgstr ""

#: admin/settings/options/rules.php:15
msgid "Selected posts"
msgstr ""

#: admin/settings/options/rules.php:16
msgid "Post has category"
msgstr ""

#: admin/settings/options/rules.php:17
msgid "Post has tag"
msgstr ""

#: admin/settings/options/rules.php:18
msgid "Posts End"
msgstr ""

#: admin/settings/options/rules.php:19
msgid "Pages"
msgstr ""

#: admin/settings/options/rules.php:20
msgid "All pages"
msgstr ""

#: admin/settings/options/rules.php:21
msgid "Selected pages"
msgstr ""

#: admin/settings/options/rules.php:22
msgid "Page type"
msgstr ""

#: admin/settings/options/rules.php:23
msgid "Pages End"
msgstr ""

#: admin/settings/options/rules.php:24
msgid "Archives"
msgstr ""

#: admin/settings/options/rules.php:25
msgid "All Archives"
msgstr ""

#: admin/settings/options/rules.php:26
msgid "All Categories"
msgstr ""

#: admin/settings/options/rules.php:27
msgid "All Tags"
msgstr ""

#: admin/settings/options/rules.php:28
msgid "All Authors"
msgstr ""

#: admin/settings/options/rules.php:29
msgid "All Dates"
msgstr ""

#: admin/settings/options/rules.php:30
msgid "Category"
msgstr ""

#: admin/settings/options/rules.php:31 classes/Admin/ListTable.php:112
msgid "Tag"
msgstr ""

#: admin/settings/options/rules.php:32
msgid "Author"
msgstr ""

#: admin/settings/options/rules.php:33
msgid "Archives End"
msgstr ""

#: admin/settings/options/rules.php:42 admin/settings/options/rules.php:57
msgid "Custom Post:"
msgstr ""

#: admin/settings/options/rules.php:44 classes/Admin/ListTable.php:340
msgid "All"
msgstr ""

#: admin/settings/options/rules.php:45
msgid "Selected"
msgstr ""

#: admin/settings/options/rules.php:53
msgid "taxonomy"
msgstr ""

#: admin/settings/options/rules.php:55
msgid "Archive"
msgstr ""

#: admin/settings/options/rules.php:61
msgid "Home Page"
msgstr ""

#: admin/settings/options/rules.php:62
msgid "Posts Page"
msgstr ""

#: admin/settings/options/rules.php:63
msgid "Search Pages"
msgstr ""

#: admin/settings/options/rules.php:64
msgid "404 Pages"
msgstr ""

#: admin/settings/options/rules.php:79
msgid "Standard"
msgstr ""

#: admin/settings/options/rules.php:80
msgid "Floating"
msgstr ""

#: admin/settings/options/rules.php:86 admin/settings/options/rules.php:94
msgid "Location"
msgstr ""

#: admin/settings/options/rules.php:88
msgid "Shortcode placement"
msgstr ""

#: admin/settings/options/rules.php:96
msgid "Top Left"
msgstr ""

#: admin/settings/options/rules.php:97
msgid "Top Center"
msgstr ""

#: admin/settings/options/rules.php:98
msgid "Top Right"
msgstr ""

#: admin/settings/options/rules.php:99
msgid "Bottom Left"
msgstr ""

#: admin/settings/options/rules.php:100
msgid "Bottom Center"
msgstr ""

#: admin/settings/options/rules.php:101
msgid "Bottom Right"
msgstr ""

#: admin/settings/options/rules.php:102 admin/settings/options/rules.php:125
msgid "Left"
msgstr ""

#: admin/settings/options/rules.php:103 admin/settings/options/rules.php:132
msgid "Right"
msgstr ""

#: admin/settings/options/rules.php:111
msgid "Top"
msgstr ""

#: admin/settings/options/rules.php:118
msgid "Bottom"
msgstr ""

#: admin/settings/options/rules.php:137
msgid "Display"
msgstr ""

#: admin/settings/options/rules.php:144
msgid "Is or is not"
msgstr ""

#: admin/settings/options/rules.php:152
msgid "Enter ID's"
msgstr ""

#: admin/settings/options/rules.php:154
msgid "Enter IDs, separated by comma."
msgstr ""

#: admin/settings/options/rules.php:161
msgid "Specific page types"
msgstr ""

#: admin/settings/options/rules.php:168
msgid "Disable Font Awesome Icon"
msgstr ""

#: admin/settings/options/rules.php:170
msgid "Disable"
msgstr ""

#: admin/settings/options/rules.php:177
msgid "Hide on smaller screens"
msgstr ""

#: admin/settings/options/rules.php:188
msgid "Hide on larger screens"
msgstr ""

#: admin/settings/options/settings.php:11
msgid "Appearance"
msgstr ""

#: admin/settings/options/settings.php:13
msgid "Only Text"
msgstr ""

#: admin/settings/options/settings.php:14
msgid "Text & Icon"
msgstr ""

#: admin/settings/options/settings.php:15
#: admin/settings/options/settings.php:46
msgid "Icon"
msgstr ""

#: admin/settings/options/settings.php:21
#: admin/settings/options/settings.php:22
msgid "Text"
msgstr ""

#: admin/settings/options/settings.php:27
msgid "Text location"
msgstr ""

#: admin/settings/options/settings.php:29
msgid "Before Icon"
msgstr ""

#: admin/settings/options/settings.php:30
msgid "After Icon"
msgstr ""

#: admin/settings/options/settings.php:31
msgid "Above the icon"
msgstr ""

#: admin/settings/options/settings.php:32
msgid "Under the icon"
msgstr ""

#: admin/settings/options/settings.php:38
msgid "Gap"
msgstr ""

#: admin/settings/options/settings.php:52
msgid "Icons"
msgstr ""

#: admin/settings/options/settings.php:62
msgid "Rotate icon"
msgstr ""

#: admin/settings/options/settings.php:68 admin/settings/options/style.php:17
msgid "Custom"
msgstr ""

#: admin/settings/options/settings.php:74 admin/settings/options/style.php:23
msgid "Degree"
msgstr ""

#: admin/settings/options/settings.php:81
msgid "Item type"
msgstr ""

#: admin/settings/options/settings.php:93
msgid "Class"
msgstr ""

#: admin/settings/options/settings.php:98
msgid "ID"
msgstr ""

#: admin/settings/options/settings.php:103
msgid "Aria label"
msgstr ""

#: admin/settings/options/style.php:11
msgid "Rotate button"
msgstr ""

#: admin/settings/options/style.php:32
msgid "z-index"
msgstr ""

#: admin/settings/options/style.php:45
msgid "Width"
msgstr ""

#: admin/settings/options/style.php:48 admin/settings/options/style.php:57
#: admin/settings/options/style.php:129
msgid "Set value with px or %"
msgstr ""

#: admin/settings/options/style.php:54
msgid "Height"
msgstr ""

#: admin/settings/options/style.php:70
msgid "Icon Color"
msgstr ""

#: admin/settings/options/style.php:80
msgid "Icon Hover Color"
msgstr ""

#: admin/settings/options/style.php:90 admin/settings/options/style.php:156
#: admin/settings/options/style.php:220
msgid "Color"
msgstr ""

#: admin/settings/options/style.php:100
msgid "Background"
msgstr ""

#: admin/settings/options/style.php:110
msgid "Hover Color"
msgstr ""

#: admin/settings/options/style.php:120
msgid "Hover Background"
msgstr ""

#: admin/settings/options/style.php:126
msgid "Radius"
msgstr ""

#: admin/settings/options/style.php:135
msgid "Style"
msgstr ""

#: admin/settings/options/style.php:138
msgid "Solid"
msgstr ""

#: admin/settings/options/style.php:139
msgid "Dotted"
msgstr ""

#: admin/settings/options/style.php:140
msgid "Dashed"
msgstr ""

#: admin/settings/options/style.php:141
msgid "Double"
msgstr ""

#: admin/settings/options/style.php:142
msgid "Groove"
msgstr ""

#: admin/settings/options/style.php:143 admin/settings/options/style.php:181
msgid "Inset"
msgstr ""

#: admin/settings/options/style.php:144 admin/settings/options/style.php:180
msgid "Outset"
msgstr ""

#: admin/settings/options/style.php:145
msgid "Ridge"
msgstr ""

#: admin/settings/options/style.php:162
msgid "Thickness"
msgstr ""

#: admin/settings/options/style.php:177
msgid "Shadow"
msgstr ""

#: admin/settings/options/style.php:188
msgid "Horizontal Position"
msgstr ""

#: admin/settings/options/style.php:195
msgid "Vertical Position"
msgstr ""

#: admin/settings/options/style.php:202
msgid "Blur"
msgstr ""

#: admin/settings/options/style.php:209
msgid "Spread"
msgstr ""

#: admin/settings/options/style.php:228
msgid "Icon Size"
msgstr ""

#: admin/settings/options/style.php:235
msgid "Font Size"
msgstr ""

#: admin/settings/options/style.php:241
msgid "Font Family"
msgstr ""

#: admin/settings/options/style.php:255
msgid "Font Weight"
msgstr ""

#: admin/settings/options/style.php:272
msgid "Font Style"
msgstr ""

#: admin/settings/pro-plugin.php:21
msgid "PRO FEATURES"
msgstr ""

#: admin/settings/sidebar.php:25
msgid "Publish"
msgstr ""

#: admin/settings/sidebar.php:29 classes/Admin/ListTable.php:116
msgid "Status"
msgstr ""

#: admin/settings/sidebar.php:32 classes/Admin/ListTable.php:284
msgid "Deactivate"
msgstr ""

#: admin/settings/sidebar.php:37 classes/Admin/ListTable.php:113
msgid "Test mode"
msgstr ""

#: admin/settings/sidebar.php:40 classes/Admin/ListTable.php:283
msgid "Activate"
msgstr ""

#: admin/settings/sidebar.php:85 classes/Admin/ListTable.php:72
msgid "Delete"
msgstr ""

#: admin/settings/statics.php:24
msgid "Analytics"
msgstr ""

#: admin/settings/statics.php:27
msgid "Views"
msgstr ""

#: admin/settings/statics.php:35
msgid "Actions"
msgstr ""

#: admin/settings/statics.php:43
msgid "Conversion"
msgstr ""

#: admin/settings/statics.php:55
msgid "Reset"
msgstr ""

#: button-generation.php:141 button-generation.php:152
msgid "Cheatin&#8217; huh?"
msgstr ""

#: classes/Admin/AdminNotices.php:51
msgid "Item Saved"
msgstr ""

#: classes/Admin/AdminNotices.php:57
msgid "Item Remove"
msgstr ""

#: classes/Admin/Dashboard.php:34
msgid "Settings"
msgstr ""

#: classes/Admin/Dashboard.php:45
msgid "Check Version"
msgstr ""

#. translators: 1: Rating link (URL), 2: Plugin name
#: classes/Admin/Dashboard.php:56
#, php-format
msgid ""
"Thank you for using <b>%2$s</b>! Please <a href=\"%1$s\" "
"target=\"_blank\">rate us</a>"
msgstr ""

#: classes/Admin/Dashboard.php:141
msgid "Add New"
msgstr ""

#: classes/Admin/Dashboard.php:175
msgid "Update"
msgstr ""

#: classes/Admin/ImporterExporter.php:36
msgid "Export All Data"
msgstr ""

#: classes/Admin/ImporterExporter.php:55
msgid "Update item if item already exists."
msgstr ""

#: classes/Admin/ImporterExporter.php:62
msgid "Import"
msgstr ""

#: classes/Admin/ImporterExporter.php:81 classes/Admin/ImporterExporter.php:94
msgid "Please select a file to import"
msgstr ""

#: classes/Admin/ImporterExporter.php:82 classes/Admin/ImporterExporter.php:89
#: classes/Admin/ImporterExporter.php:95
msgid "Error"
msgstr ""

#: classes/Admin/ImporterExporter.php:88
msgid "Please upload a valid .json file"
msgstr ""

#: classes/Admin/ListTable.php:64
msgid "Untitled"
msgstr ""

#: classes/Admin/ListTable.php:68
msgid "Edit"
msgstr ""

#: classes/Admin/ListTable.php:70
msgid "Duplicate"
msgstr ""

#: classes/Admin/ListTable.php:74
msgid "Export"
msgstr ""

#: classes/Admin/ListTable.php:78
msgid "View"
msgstr ""

#: classes/Admin/ListTable.php:110
msgid "Title"
msgstr ""

#: classes/Admin/ListTable.php:114
msgid "The item will only be displayed for administrators."
msgstr ""

#: classes/Admin/ListTable.php:117
msgid "Display item on the Frontend."
msgstr ""

#: classes/Admin/ListTable.php:152
msgid "UnTitle"
msgstr ""

#: classes/Admin/ListTable.php:153
msgid "Click for Deactivate."
msgstr ""

#: classes/Admin/ListTable.php:154
msgid "Click for Activate."
msgstr ""

#: classes/Admin/ListTable.php:155 classes/Admin/ListTable.php:164
msgid "OFF"
msgstr ""

#: classes/Admin/ListTable.php:157 classes/Admin/ListTable.php:166
msgid "ON"
msgstr ""

#: classes/Admin/ListTable.php:161
msgid "Click for OFF."
msgstr ""

#: classes/Admin/ListTable.php:162
msgid "Click for ON."
msgstr ""

#: classes/Admin/ListTable.php:282
msgid "Delate"
msgstr ""

#: classes/Admin/ListTable.php:285
msgid "Test mode ON"
msgstr ""

#: classes/Admin/ListTable.php:286
msgid "Test mode OFF"
msgstr ""

#: classes/Admin/ListTable.php:337
msgid "Filter by tag"
msgstr ""

#: classes/Admin/ListTable.php:353
msgid "Filter"
msgstr ""

#: classes/Admin/SupportForm.php:36
msgid "Support Form"
msgstr ""

#: classes/Admin/SupportForm.php:41
msgid "Your Name"
msgstr ""

#: classes/Admin/SupportForm.php:49
msgid "Contact email"
msgstr ""

#: classes/Admin/SupportForm.php:61
msgid "Link to the issue"
msgstr ""

#: classes/Admin/SupportForm.php:70
msgid "Message type"
msgstr ""

#: classes/Admin/SupportForm.php:73
msgid "Issue"
msgstr ""

#: classes/Admin/SupportForm.php:74
msgid "Idea"
msgstr ""

#: classes/Admin/SupportForm.php:82
msgid "Plugin"
msgstr ""

#: classes/Admin/SupportForm.php:91
msgid "License Key"
msgstr ""

#: classes/Admin/SupportForm.php:102
msgid "Enter Your Message"
msgstr ""

#: classes/Admin/SupportForm.php:113
msgid "Send to Support"
msgstr ""

#: classes/Admin/SupportForm.php:179
msgid "Your message has been sent to the support team."
msgstr ""

#: classes/Admin/SupportForm.php:182
msgid "Sorry, but message did not send. Please, contact us via support page."
msgstr ""

#: classes/Admin/SupportForm.php:194
msgid "Please fill in all the form fields below."
msgstr ""

#: includes/class-wow-company.php:27
msgid "WordPress plugins from Wow-Company"
msgstr ""

#: includes/class-wow-company.php:28
msgid "Wow Plugins"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "Button Generator"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://wordpress.org/plugins/button-generation/"
msgstr ""

#. Description of the plugin/theme
msgid "Easy generation of custom buttons."
msgstr ""

#. Author of the plugin/theme
msgid "Wow-Company"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://wow-estore.com/"
msgstr ""
