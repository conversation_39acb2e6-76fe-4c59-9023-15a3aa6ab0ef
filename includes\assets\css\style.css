.wpie-page .about-wrap{
	text-align: center;
}

.wpie-badge img {
	width: 52px;
}

.wpie-page__header {
	max-width: 750px;
	margin: 50px auto;
	text-align: center;
}

.wpie-page__header h1 {
	letter-spacing: .25rem;
	text-transform: uppercase;
	font-size: 20px;
	font-weight: normal;
}

.wpie-page__header .about-text {
	color: rgba(35, 28, 51, 0.66);
	font-size: 18px;
}

.wpie-page__content {
	max-width: 1216px;
	margin: 50px auto;
}

.wpie-page__content h2:before {
	background-color: #E86E2C;
	content: "";
	display: inline-block;
	height: 0.125rem;
	margin-right: 1rem;
	vertical-align: 0.3em;
	width: 1.5rem;
}

.item-cards {
	display: grid;
	gap: 0.75rem;
	margin-bottom: 48px;
}

.item-card {
	margin: 0;
	padding: 1.25rem;
	border-radius: 4px;
	border: 1px solid #c3c4c7;
	background-color: #ffffff;

	display: grid;
	grid-template-columns: 48px 1fr;
	grid-template-rows: repeat(2, 1fr);
	grid-column-gap: 1rem;
	grid-row-gap: 8px;
}

.item-card:hover {
	box-shadow: 0 0 0 1px rgba(20, 16, 44, 0.05), 0 1px 0 0 rgba(20, 16, 44, 0.05), 0 0.2em 1.6em -0.8em rgba(20, 16, 44, 0.2), 0 0.4em 2.4em -1em rgba(20, 16, 44, 0.3), 0 0.4em 0.8em -1.2em rgba(20, 16, 44, 0.4), 0 0.8em 1.2em -1.6em rgba(20, 16, 44, 0.5), 0 1.2em 1.6em -2em rgba(20, 16, 44, 0.6);
}

.item-description {
	grid-area: 2 / 1 / 3 / 3;
	font-size: 15px;
	font-weight: normal;
	color: rgba(35, 28, 51, 0.73);
}


.item-img img {
	width: 48px;
	border-radius: 2px;
}

.item-title {
	color: #363636;
	font-weight: 600;
	line-height: 1.125;
	font-size: 1.125rem;
}

.item-links {
	display: flex;
	gap: 12px;
	font-size: 14px;
	padding-top: 8px;
}

@media (min-width: 769px) {
	.item-cards {
		grid-template-columns: repeat(2, minmax(0, 1fr));
		gap: 0.75rem;
	}
}

@media (min-width: 1024px) {
	.item-cards {
		grid-template-columns: repeat(3, minmax(0, 1fr));
		gap: 1rem;
	}
}

.wpie-page__footer {
	max-width: 750px;
	margin-inline: auto;
}
.wpie-page__footer p {
	color: #14102C;
	margin-bottom: 5px;
}