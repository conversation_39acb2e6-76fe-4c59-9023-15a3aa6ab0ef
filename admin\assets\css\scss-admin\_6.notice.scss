.wpie-notice {
  position: fixed !important;
  top: 46px;
  right: 16px;
  margin: 0 !important;
  border: none;
  color: #ffffff;
  font-size: 15px;
  padding: 12px 16px;
  border-radius: 0 0 12px 12px;
  letter-spacing: 1px;
  transform: translateY(-100%);
  transition: transform 0.2s;
  &.is-active {
    transform: translateY(0);
    z-index: 9;
  }
  @media screen and (min-width: 783px){
    top: 32px;
  }
  .notice-dismiss {
    padding: 12px;
    &:before {
      color: #ffffff;
      border-radius: 4px;
      box-shadow: var(--wpie-shadow-small);
    }
  }
  &.notice-warning {
    background-color: var(--wpie-color-danger);
    .notice-dismiss {
      &:before {
        background-color: #BA526E;
      }
    }
  }
  &.notice-success {
    background-color: var(--wpie-color-success);
    .notice-dismiss {
      &:before {
        background-color: #6EBA52;
      }
    }
  }
}