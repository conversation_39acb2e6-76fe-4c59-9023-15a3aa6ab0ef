/* fontIconPicker main CSS file {@link https://github.com/micc83/fontIconPicker} */ .icons-selector *{margin:0;padding:0;border:0;font:inherit;vertical-align:baseline;font-family:"Font Awesome 5 Free",HelveticaNeue-Light,"Helvetica Neue Light","Helvetica Neue",Helvetica,Arial,"Lucida Grande",sans-serif !important;}.fas { font-weight:900; } .far{ font-weight:400; } .fab { font-family: 'Font Awesome 5 Brands'; font-weight:400; }.icons-selector,.icons-selector *,.icons-selector :after,.icons-selector :before,.icons-selector:after,.icons-selector:before{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}.icons-selector{display:inline-block;vertical-align:middle;text-align:left}.icons-selector .selector{width:100px;height:40px}.icons-selector .selector-button{width:39px;height:100%;display:block;text-align:center;cursor:pointer;float:left}.icons-selector .selector-button i{line-height:38px;text-align:center}.icons-selector .selected-icon{display:block;width:60px;height:100%;float:left;text-align:center}.icons-selector .selected-icon i{line-height:40px;font-size:18px;cursor:default}.icons-selector .selector-popup{position:absolute;z-index:10000;background-color:#fefefe;padding:5px;height:auto;width:342px;margin-top:-1px}.icons-selector .selector-category select,.icons-selector .selector-search input[type=text]{border:0;line-height:20px;padding:10px 2.5%;width:100%;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;margin-bottom:5px;font-size:12px;display:block}.icons-selector .selector-category select{height:40px}.icons-selector .selector-category select option{padding:10px}.icons-selector input::-webkit-input-placeholder{text-transform:uppercase}.icons-selector input:-moz-placeholder{text-transform:uppercase}.icons-selector input::-moz-placeholder{text-transform:uppercase}.icons-selector input:-ms-input-placeholder{text-transform:uppercase}.icons-selector .selector-search{position:relative}.icons-selector .selector-search i{position:absolute;right:10px;top:7px}.icons-selector .fip-icons-container{width:100%;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;padding:5px}.icons-selector .fip-icons-container .loading{font-size:24px;margin:0 auto;padding:20px 0;text-align:center;width:100%}.icons-selector .fip-box{display:inline-block;margin:2px;width:60px;line-height:42px;text-align:center;cursor:pointer;vertical-align:top;height:40px}.icons-selector .selector-footer{line-height:12px;padding:5px 5px 0;text-align:center}.icons-selector .selector-footer,.icons-selector .selector-footer i{font-size:14px}.icons-selector .selector-arrows{float:right}.icons-selector .selector-pages{font-size:11px;float:left}.icons-selector .selector-arrows i{cursor:pointer}.icons-selector .selector-footer em{font-style:italic}.icons-selector .icons-picker-error i:before{color:#eee}@font-face{font-family:iconpicker;src:url(iconpicker.eot?90190138);src:url(iconpicker.eot?90190138#iefix) format('embedded-opentype'),url(iconpicker.woff?90190138) format('woff'),url(iconpicker.ttf?90190138) format('truetype'),url(iconpicker.svg?90190138#iconpicker) format('svg');font-weight:400;font-style:normal}.icons-selector [class*=" fip-icon-"]:before,.icons-selector [class^=fip-icon-]:before{font-family:iconpicker;font-style:normal;font-weight:400;speak:none;display:inline-block;text-decoration:inherit;width:1em;margin-right:.2em;text-align:center;font-variant:normal;text-transform:none;line-height:1em;margin-left:.2em}.icons-selector .fip-icon-search:before{content:'\e812';cursor:default}.icons-selector .fip-icon-cancel:before{content:'\e814';cursor:pointer}.icons-selector .fip-icon-block:before{content:'\e84e';color:#fed0d0}.icons-selector .fip-icon-down-dir:before{content:'\e800'}.icons-selector .fip-icon-up-dir:before{content:'\e813'}.icons-selector .fip-icon-left-dir:before{content:'\e801'}.icons-selector .fip-icon-right-dir:before{content:'\e802'}.icons-selector .fip-icon-spin3:before{content:'\e815'}.icons-selector .fip-icon-spin3{-moz-animation:spin 2s infinite linear;-o-animation:spin 2s infinite linear;-webkit-animation:spin 2s infinite linear;animation:spin 2s infinite linear;display:inline-block}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);-o-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(359deg);-o-transform:rotate(359deg);-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@-webkit-keyframes spin{0%{-moz-transform:rotate(0deg);-o-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(359deg);-o-transform:rotate(359deg);-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@-o-keyframes spin{0%{-moz-transform:rotate(0deg);-o-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(359deg);-o-transform:rotate(359deg);-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@-ms-keyframes spin{0%{-moz-transform:rotate(0deg);-o-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(359deg);-o-transform:rotate(359deg);-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes spin{0%{-moz-transform:rotate(0deg);-o-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(359deg);-o-transform:rotate(359deg);-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.icons-search-input {font-family:Helvetica,Arial,"Lucida Grande",sans-serif}