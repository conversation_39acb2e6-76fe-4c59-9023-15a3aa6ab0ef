<?php
/**
 * Class Wow_Company
 *
 * The Wow_Company class represents a company in the WordPress admin menu that provides Wow Plugins.
 *
 * @package    WowPlugin
 * @subpackage General
 * <AUTHOR> <<EMAIL>>, Wow-Company
 * @copyright  2024 Dmytro Lobov
 * @license    GPL-2.0+
 */


defined( 'ABSPATH' ) || exit;

final class Wow_Company {
	public function __construct() {

		add_action( 'admin_menu', [ $this, 'add_menu' ] );
		add_action( 'admin_enqueue_scripts', [ $this, 'admin_style' ] );

	}

	public function add_menu(): void {
		$icon       = self::icon();
		$page_title = __( 'WordPress plugins from Wow-Company', 'button-generation' );
		$menu_title = __( 'Wow Plugins', 'button-generation' );
		$capability = 'manage_options';
		$slug       = 'wow-company';

		add_menu_page( $page_title, $menu_title, $capability, $slug, [ $this, 'welcome_page' ], $icon );
		add_submenu_page( $slug, $page_title, '👋 Hey', $capability, $slug );
	}

	public function welcome_page(): void {
		require_once plugin_dir_path( __FILE__ ) . 'page-welcome.php';
		wp_enqueue_style( 'admin-wpie-page', plugin_dir_url( __FILE__ ) . 'assets/css/style.css', null, '1.0' );
	}

	public function admin_style(): void {
		wp_enqueue_style( 'wow-plugins', plugin_dir_url( __FILE__ ) . 'assets/css/admin.css', null, '1.0' );
	}


	private static function icon(): string {
		return 'data:image/svg+xml;base64, ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

	}

}

new Wow_Company;