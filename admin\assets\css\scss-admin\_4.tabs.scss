.wpie-tabs {
  background: rgba(var(--wpie-rgb-blue),0.01);
  //background-color: rgba(var(--wpie-rgb-blue), 0.02);
  border-radius: 0.35em;
  box-shadow: inset 0 0 0 1px rgba(var(--wpie-rgb-dark), 0.04), inset 0 0.25em 0.5em -0.25em rgba(var(--wpie-rgb-dark), 0.08);
  display: flex;
  gap: 1.25rem;
  justify-content: flex-start;
  overflow: hidden;
  padding: 0.5rem 1.25rem;
}

.wpie-tab-label {
  border-radius: 0.35em;
  color: rgba(var(--wpie-rgb-dark), 0.6);
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  line-height: 1em;
  letter-spacing: -0.02em;
  padding: 0.7em 1em;
  transition: background var(--wpie-transition), box-shadow var(--wpie-transition), color var(--wpie-transition);
  white-space: nowrap;

  &.selected {
    background: #ffffff;
    box-shadow: var(--wpie-shadow-small);
    color: rgba(var(--wpie-rgb-dark), 1);
  }
}

.wpie-tabs-contents {
  position: relative;
}

.wpie-tab-content {
  height: 0;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  left: -999999px;
  top: -9999%;
  //display: none;
}

input[type="radio"].wpie-tab-toggle {
  display: none;
}

.wpie-tab-toggle:checked + .wpie-tab-content {
  height: auto;
  opacity: 1;
  visibility: visible;
  position: relative;
  left: 0;
  top: 0;
}

.wpie-tab-content {
  border-radius: 0.2rem;
  box-shadow: var(--wpie-shadow);
  width: 100%;
  padding: 1.25rem;
  box-sizing: border-box;
  background-color: #ffffff;
}

.wpie-tabs-link {
  background: #ffffff;
  border-radius: 0.35em;
  //box-shadow: inset 0 0 0 1px rgba(var(--wpie-rgb-dark), 0.04), inset 0 0.25em 0.5em -0.25em rgba(var(--wpie-rgb-dark), 0.08);
  display: inline-flex;
  gap: 0.25rem;
  justify-content: center;
  overflow: hidden;
  padding: 0.5rem 1.25rem;
  margin-block: 15px;
  margin-inline: -1.25rem;

}

.wpie-tab__link {
  border-radius: 0.35em;
  color: rgba(var(--wpie-rgb-dark), 0.6);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  line-height: 1em;
  letter-spacing: -0.02em;
  padding: 0.5em .75em;
  transition: background var(--wpie-transition), box-shadow var(--wpie-transition), color var(--wpie-transition);
  white-space: nowrap;


  &.is-active,
  &:hover {
    background: #ffffff;
    box-shadow: var(--wpie-shadow-small);
  }
  &.is-active {
    color: rgb(var(--wpie-rgb-blurple));
  }
  &:hover {
    color: rgb(var(--wpie-rgb-dark));
  }
}

.wpie-tab-settings {
  height: 0;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  left: -999999px;
  top: -999999px;
  display: none;

  &.is-active {
    height: auto;
    opacity: 1;
    visibility: visible;
    position: relative;
    left: 0;
    top: 0;
    display: block;
  }
}


